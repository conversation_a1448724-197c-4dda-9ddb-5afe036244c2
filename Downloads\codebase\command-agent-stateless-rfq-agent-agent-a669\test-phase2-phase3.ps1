#!/usr/bin/env pwsh

# Test Script for Phase 2 & Phase 3 Implementation
# Tests all new features including agent discovery, state management, monitoring, and performance optimization

Write-Host "🧪 Testing Phase 2 & Phase 3 Implementation" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Configuration
$AGENT_SERVICE_PORT = 8000
$API_GATEWAY_PORT = 3101
$FRONTEND_PORT = 3100
$REDIS_PORT = 6379

# Test results tracking
$TestResults = @()

function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$ExpectedStatus = 200
    )
    
    try {
        Write-Host "Testing $ServiceName..." -ForegroundColor Yellow
        $response = Invoke-RestMethod -Uri $Url -Method GET -TimeoutSec 10
        
        if ($response) {
            Write-Host "✅ $ServiceName is healthy" -ForegroundColor Green
            $script:TestResults += @{
                Service = $ServiceName
                Status = "PASS"
                Details = "Service responding correctly"
            }
            return $true
        }
    }
    catch {
        Write-Host "❌ $ServiceName failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = $ServiceName
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
}

function Test-AgentDiscovery {
    Write-Host "Testing Agent Discovery API..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:$API_GATEWAY_PORT/api/agents/discover" -Method GET
        
        if ($response.success -and $response.agents.Count -gt 0) {
            Write-Host "✅ Agent Discovery working - Found $($response.agents.Count) agents" -ForegroundColor Green
            
            foreach ($agent in $response.agents) {
                Write-Host "  - $($agent.agent_id): $($agent.agent_type)" -ForegroundColor Gray
            }
            
            $script:TestResults += @{
                Service = "Agent Discovery"
                Status = "PASS"
                Details = "Found $($response.agents.Count) agents"
            }
            return $true
        }
        else {
            throw "No agents discovered or invalid response"
        }
    }
    catch {
        Write-Host "❌ Agent Discovery failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = "Agent Discovery"
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
}

function Test-HealthEndpoints {
    Write-Host "Testing Health Check Endpoints..." -ForegroundColor Yellow
    
    $healthEndpoints = @(
        @{ Name = "API Gateway Health"; Url = "http://localhost:$API_GATEWAY_PORT/health" },
        @{ Name = "Frontend Health"; Url = "http://localhost:$API_GATEWAY_PORT/api/health/frontend" }
    )
    
    $allPassed = $true
    
    foreach ($endpoint in $healthEndpoints) {
        try {
            $response = Invoke-RestMethod -Uri $endpoint.Url -Method GET
            
            if ($response.status -eq "healthy") {
                Write-Host "✅ $($endpoint.Name) is healthy" -ForegroundColor Green
                $script:TestResults += @{
                    Service = $endpoint.Name
                    Status = "PASS"
                    Details = "Health check passed"
                }
            }
            else {
                throw "Health check returned non-healthy status: $($response.status)"
            }
        }
        catch {
            Write-Host "❌ $($endpoint.Name) failed: $($_.Exception.Message)" -ForegroundColor Red
            $script:TestResults += @{
                Service = $endpoint.Name
                Status = "FAIL"
                Details = $_.Exception.Message
            }
            $allPassed = $false
        }
    }
    
    return $allPassed
}

function Test-ExportEndpoint {
    Write-Host "Testing Export Functionality..." -ForegroundColor Yellow
    
    try {
        $testData = @{
            format = "json"
            data = @{
                test = "data"
                timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
            }
            includeCharts = $true
        }
        
        $body = $testData | ConvertTo-Json -Depth 3
        $response = Invoke-RestMethod -Uri "http://localhost:$API_GATEWAY_PORT/api/export/test-execution-123" -Method POST -Body $body -ContentType "application/json"
        
        if ($response.executionId -eq "test-execution-123" -and $response.format -eq "json") {
            Write-Host "✅ Export functionality working" -ForegroundColor Green
            $script:TestResults += @{
                Service = "Export API"
                Status = "PASS"
                Details = "Export endpoint responding correctly"
            }
            return $true
        }
        else {
            throw "Invalid export response"
        }
    }
    catch {
        Write-Host "❌ Export functionality failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = "Export API"
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
}

function Test-WebSocketEndpoint {
    Write-Host "Testing WebSocket Endpoint..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:$API_GATEWAY_PORT/api/ws/test-execution-456" -Method GET
        
        if ($response.executionId -eq "test-execution-456" -and $response.wsUrl) {
            Write-Host "✅ WebSocket endpoint available" -ForegroundColor Green
            Write-Host "  WebSocket URL: $($response.wsUrl)" -ForegroundColor Gray
            $script:TestResults += @{
                Service = "WebSocket API"
                Status = "PASS"
                Details = "WebSocket endpoint responding"
            }
            return $true
        }
        else {
            throw "Invalid WebSocket response"
        }
    }
    catch {
        Write-Host "❌ WebSocket endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = "WebSocket API"
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
}

function Test-FrontendBuild {
    Write-Host "Testing Frontend Build..." -ForegroundColor Yellow
    
    try {
        Push-Location "app"
        
        # Check if node_modules exists
        if (-not (Test-Path "node_modules")) {
            Write-Host "Installing dependencies..." -ForegroundColor Yellow
            npm install
        }
        
        # Test build
        Write-Host "Testing build process..." -ForegroundColor Yellow
        $buildOutput = npm run build 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Frontend build successful" -ForegroundColor Green
            $script:TestResults += @{
                Service = "Frontend Build"
                Status = "PASS"
                Details = "Build completed successfully"
            }
            return $true
        }
        else {
            throw "Build failed with exit code $LASTEXITCODE"
        }
    }
    catch {
        Write-Host "❌ Frontend build failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = "Frontend Build"
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
    finally {
        Pop-Location
    }
}

function Test-TypeScriptCompilation {
    Write-Host "Testing TypeScript Compilation..." -ForegroundColor Yellow
    
    try {
        Push-Location "app"
        
        # Test TypeScript compilation
        Write-Host "Checking TypeScript compilation..." -ForegroundColor Yellow
        $tscOutput = npx tsc --noEmit 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ TypeScript compilation successful" -ForegroundColor Green
            $script:TestResults += @{
                Service = "TypeScript Compilation"
                Status = "PASS"
                Details = "No TypeScript errors found"
            }
            return $true
        }
        else {
            Write-Host "TypeScript errors found:" -ForegroundColor Yellow
            Write-Host $tscOutput -ForegroundColor Gray
            $script:TestResults += @{
                Service = "TypeScript Compilation"
                Status = "WARN"
                Details = "TypeScript errors found but build may still work"
            }
            return $false
        }
    }
    catch {
        Write-Host "❌ TypeScript compilation failed: $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{
            Service = "TypeScript Compilation"
            Status = "FAIL"
            Details = $_.Exception.Message
        }
        return $false
    }
    finally {
        Pop-Location
    }
}

function Show-TestSummary {
    Write-Host "`n📊 Test Summary" -ForegroundColor Cyan
    Write-Host "===============" -ForegroundColor Cyan
    
    $passCount = ($TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failCount = ($TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $warnCount = ($TestResults | Where-Object { $_.Status -eq "WARN" }).Count
    $totalCount = $TestResults.Count
    
    Write-Host "Total Tests: $totalCount" -ForegroundColor White
    Write-Host "Passed: $passCount" -ForegroundColor Green
    Write-Host "Failed: $failCount" -ForegroundColor Red
    Write-Host "Warnings: $warnCount" -ForegroundColor Yellow
    
    Write-Host "`nDetailed Results:" -ForegroundColor White
    Write-Host "-----------------" -ForegroundColor White
    
    foreach ($result in $TestResults) {
        $color = switch ($result.Status) {
            "PASS" { "Green" }
            "FAIL" { "Red" }
            "WARN" { "Yellow" }
            default { "White" }
        }
        
        Write-Host "$($result.Status.PadRight(4)) | $($result.Service.PadRight(25)) | $($result.Details)" -ForegroundColor $color
    }
    
    if ($failCount -eq 0) {
        Write-Host "`n🎉 All critical tests passed! Phase 2 & 3 implementation is ready." -ForegroundColor Green
    }
    else {
        Write-Host "`n⚠️  Some tests failed. Please review the issues above." -ForegroundColor Yellow
    }
}

# Main test execution
Write-Host "Starting comprehensive testing..." -ForegroundColor White

# Test 1: Basic service health
Write-Host "`n1️⃣ Testing Basic Service Health" -ForegroundColor Cyan
Test-Service "API Gateway" "http://localhost:$API_GATEWAY_PORT/"

# Test 2: Health endpoints
Write-Host "`n2️⃣ Testing Health Endpoints" -ForegroundColor Cyan
Test-HealthEndpoints

# Test 3: Agent discovery
Write-Host "`n3️⃣ Testing Agent Discovery" -ForegroundColor Cyan
Test-AgentDiscovery

# Test 4: Export functionality
Write-Host "`n4️⃣ Testing Export Functionality" -ForegroundColor Cyan
Test-ExportEndpoint

# Test 5: WebSocket endpoint
Write-Host "`n5️⃣ Testing WebSocket Endpoint" -ForegroundColor Cyan
Test-WebSocketEndpoint

# Test 6: Frontend build
Write-Host "`n6️⃣ Testing Frontend Build" -ForegroundColor Cyan
Test-FrontendBuild

# Test 7: TypeScript compilation
Write-Host "`n7️⃣ Testing TypeScript Compilation" -ForegroundColor Cyan
Test-TypeScriptCompilation

# Show summary
Show-TestSummary

Write-Host "`n🏁 Testing completed!" -ForegroundColor Cyan
