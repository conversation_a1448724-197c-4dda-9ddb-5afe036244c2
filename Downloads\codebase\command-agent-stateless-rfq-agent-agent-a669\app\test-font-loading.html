<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Loading Test - Procure.ai</title>
    
    <!-- Font Preloading for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link 
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" 
      rel="stylesheet" 
    />
    
    <style>
        /* Critical CSS for font loading */
        :root {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            font-feature-settings: 'cv11', 'ss01';
            font-variation-settings: 'opsz' 32;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        body {
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
            color: #1e293b;
            font-display: swap;
            visibility: visible;
            opacity: 1;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #0f172a;
        }
        
        h2 {
            font-size: 1.875rem;
            font-weight: 600;
            margin: 2rem 0 1rem 0;
            color: #334155;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .font-weights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .weight-sample {
            padding: 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }
        
        .status.loading {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status.loaded {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .metric {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Font Loading Test - Procure.ai</h1>
        <p>This page tests the font loading implementation to ensure no FOUC (Flash of Unstyled Content) occurs.</p>
        
        <div class="test-section">
            <h2>Font Loading Status <span id="font-status" class="status loading">Loading...</span></h2>
            <p>Current font loading state and performance metrics.</p>
            
            <div class="performance-metrics">
                <div class="metric">
                    <div id="load-time" class="metric-value">--</div>
                    <div class="metric-label">Load Time (ms)</div>
                </div>
                <div class="metric">
                    <div id="font-count" class="metric-value">--</div>
                    <div class="metric-label">Fonts Loaded</div>
                </div>
                <div class="metric">
                    <div id="render-time" class="metric-value">--</div>
                    <div class="metric-label">Render Time (ms)</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Font Weight Samples</h2>
            <p>Testing different font weights to ensure consistent rendering:</p>
            
            <div class="font-weights">
                <div class="weight-sample">
                    <div style="font-weight: 100;">Thin (100)</div>
                    <div style="font-weight: 200;">Extra Light (200)</div>
                    <div style="font-weight: 300;">Light (300)</div>
                </div>
                <div class="weight-sample">
                    <div style="font-weight: 400;">Regular (400)</div>
                    <div style="font-weight: 500;">Medium (500)</div>
                    <div style="font-weight: 600;">Semi Bold (600)</div>
                </div>
                <div class="weight-sample">
                    <div style="font-weight: 700;">Bold (700)</div>
                    <div style="font-weight: 800;">Extra Bold (800)</div>
                    <div style="font-weight: 900;">Black (900)</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Typography Samples</h2>
            <p>Sample text to verify consistent font rendering across different elements:</p>
            
            <h1>Heading 1 - Procure.ai Platform</h1>
            <h2>Heading 2 - AI-Powered Procurement</h2>
            <h3>Heading 3 - Agent Management System</h3>
            
            <p><strong>Bold text:</strong> This is a sample of bold text to test font weight rendering.</p>
            <p><em>Italic text:</em> This is a sample of italic text to test font style rendering.</p>
            <p>Regular paragraph text with <code style="font-family: 'JetBrains Mono', monospace; background: #f1f5f9; padding: 2px 4px; border-radius: 3px;">inline code</code> elements.</p>
            
            <blockquote style="border-left: 4px solid #3b82f6; padding-left: 1rem; margin: 1rem 0; font-style: italic; color: #64748b;">
                "The font loading implementation should prevent any flash of unstyled content and ensure smooth typography rendering."
            </blockquote>
        </div>
        
        <div class="test-section">
            <h2>Performance Analysis</h2>
            <div id="performance-log" style="font-family: monospace; font-size: 0.875rem; background: #1e293b; color: #e2e8f0; padding: 1rem; border-radius: 6px; max-height: 200px; overflow-y: auto;">
                Initializing font loading test...
            </div>
        </div>
    </div>

    <script>
        // Font loading performance test
        const startTime = performance.now();
        let fontLoadTime = null;
        let renderTime = null;
        
        const log = (message) => {
            const logElement = document.getElementById('performance-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `\n[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        };
        
        const updateMetrics = () => {
            if (fontLoadTime) {
                document.getElementById('load-time').textContent = Math.round(fontLoadTime);
            }
            if (renderTime) {
                document.getElementById('render-time').textContent = Math.round(renderTime);
            }
        };
        
        // Check if Font Loading API is supported
        if ('fonts' in document) {
            log('Font Loading API supported');
            
            // Monitor font loading
            document.fonts.ready.then(() => {
                fontLoadTime = performance.now() - startTime;
                log(`Fonts loaded in ${Math.round(fontLoadTime)}ms`);
                
                document.getElementById('font-status').textContent = 'Loaded';
                document.getElementById('font-status').className = 'status loaded';
                document.getElementById('font-count').textContent = document.fonts.size;
                
                updateMetrics();
            });
            
            // Log individual font loads
            document.fonts.addEventListener('loadingdone', (event) => {
                event.fontfaces.forEach(fontface => {
                    log(`Font loaded: ${fontface.family} ${fontface.weight} ${fontface.style}`);
                });
            });
            
            document.fonts.addEventListener('loadingerror', (event) => {
                event.fontfaces.forEach(fontface => {
                    log(`Font failed: ${fontface.family} ${fontface.weight} ${fontface.style}`);
                });
                document.getElementById('font-status').textContent = 'Error';
                document.getElementById('font-status').className = 'status error';
            });
        } else {
            log('Font Loading API not supported, using fallback detection');
            
            // Fallback font detection
            setTimeout(() => {
                fontLoadTime = performance.now() - startTime;
                log(`Estimated font load time: ${Math.round(fontLoadTime)}ms`);
                
                document.getElementById('font-status').textContent = 'Loaded (Fallback)';
                document.getElementById('font-status').className = 'status loaded';
                document.getElementById('font-count').textContent = '1';
                
                updateMetrics();
            }, 1000);
        }
        
        // Measure render time
        window.addEventListener('load', () => {
            renderTime = performance.now() - startTime;
            log(`Page rendered in ${Math.round(renderTime)}ms`);
            updateMetrics();
        });
        
        // Initial log
        log('Font loading test started');
        log(`User Agent: ${navigator.userAgent}`);
        log(`Screen: ${screen.width}x${screen.height}`);
    </script>
</body>
</html>
