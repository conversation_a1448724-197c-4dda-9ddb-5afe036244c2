/**
 * Application State Management with Zustand
 * Centralized state management with persistence and optimistic updates
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { type AgentCapabilities, type WorkflowStatus, type AgentExecutionResponse } from '../services/agentService';

// ========== TYPE DEFINITIONS ==========

export interface WorkflowExecution {
  id: string;
  agentId: string;
  agentCapabilities: AgentCapabilities;
  request: any;
  status: WorkflowStatus | null;
  result: AgentExecutionResponse | null;
  startTime: string;
  endTime?: string;
  error?: string;
}

export interface AppNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  persistent?: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultAgent: string;
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
  };
  export: {
    defaultFormat: 'pdf' | 'excel' | 'json';
    includeCharts: boolean;
  };
}

export interface AppState {
  // Agent Management
  availableAgents: AgentCapabilities[];
  selectedAgent: AgentCapabilities | null;
  agentsLoading: boolean;
  agentsError: string | null;

  // Workflow Management
  activeExecutions: Record<string, WorkflowExecution>;
  executionHistory: WorkflowExecution[];
  currentExecution: WorkflowExecution | null;

  // UI State
  sidebarOpen: boolean;
  currentView: 'home' | 'agents' | 'execution' | 'results' | 'history';
  notifications: AppNotification[];
  loading: Record<string, boolean>;
  errors: Record<string, string>;

  // User Preferences
  preferences: UserPreferences;

  // Cache
  cache: Record<string, { data: any; timestamp: number; ttl: number }>;
}

export interface AppActions {
  // Agent Actions
  setAvailableAgents: (agents: AgentCapabilities[]) => void;
  setSelectedAgent: (agent: AgentCapabilities | null) => void;
  setAgentsLoading: (loading: boolean) => void;
  setAgentsError: (error: string | null) => void;

  // Workflow Actions
  startExecution: (execution: Omit<WorkflowExecution, 'id' | 'startTime'>) => string;
  updateExecution: (id: string, updates: Partial<WorkflowExecution>) => void;
  completeExecution: (id: string, result: AgentExecutionResponse) => void;
  failExecution: (id: string, error: string) => void;
  setCurrentExecution: (execution: WorkflowExecution | null) => void;
  clearExecutionHistory: () => void;

  // UI Actions
  setSidebarOpen: (open: boolean) => void;
  setCurrentView: (view: AppState['currentView']) => void;
  setLoading: (key: string, loading: boolean) => void;
  setError: (key: string, error: string | null) => void;
  clearErrors: () => void;

  // Notification Actions
  addNotification: (notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Preference Actions
  updatePreferences: (updates: Partial<UserPreferences>) => void;
  resetPreferences: () => void;

  // Cache Actions
  setCache: (key: string, data: any, ttl?: number) => void;
  getCache: (key: string) => any | null;
  clearCache: (key?: string) => void;

  // Utility Actions
  reset: () => void;
}

// ========== DEFAULT STATE ==========

const defaultPreferences: UserPreferences = {
  theme: 'system',
  defaultAgent: 'rfq_agent',
  autoRefresh: true,
  refreshInterval: 5000,
  notifications: {
    enabled: true,
    sound: false,
    desktop: true,
  },
  export: {
    defaultFormat: 'pdf',
    includeCharts: true,
  },
};

const initialState: AppState = {
  availableAgents: [],
  selectedAgent: null,
  agentsLoading: false,
  agentsError: null,
  activeExecutions: {},
  executionHistory: [],
  currentExecution: null,
  sidebarOpen: true,
  currentView: 'home',
  notifications: [],
  loading: {},
  errors: {},
  preferences: defaultPreferences,
  cache: {},
};

// ========== STORE IMPLEMENTATION ==========

export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Agent Actions
        setAvailableAgents: (agents) =>
          set((state) => {
            state.availableAgents = agents;
            state.agentsError = null;
          }),

        setSelectedAgent: (agent) =>
          set((state) => {
            state.selectedAgent = agent;
          }),

        setAgentsLoading: (loading) =>
          set((state) => {
            state.agentsLoading = loading;
          }),

        setAgentsError: (error) =>
          set((state) => {
            state.agentsError = error;
            state.agentsLoading = false;
          }),

        // Workflow Actions
        startExecution: (execution) => {
          const id = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
          const newExecution: WorkflowExecution = {
            ...execution,
            id,
            startTime: new Date().toISOString(),
          };

          set((state) => {
            state.activeExecutions[id] = newExecution;
            state.currentExecution = newExecution;
          });

          return id;
        },

        updateExecution: (id, updates) =>
          set((state) => {
            if (state.activeExecutions[id]) {
              Object.assign(state.activeExecutions[id], updates);
              
              if (state.currentExecution?.id === id) {
                Object.assign(state.currentExecution, updates);
              }
            }
          }),

        completeExecution: (id, result) =>
          set((state) => {
            if (state.activeExecutions[id]) {
              const execution = state.activeExecutions[id];
              execution.result = result;
              execution.endTime = new Date().toISOString();
              
              // Move to history
              state.executionHistory.unshift(execution);
              delete state.activeExecutions[id];
              
              // Clear current if it's this execution
              if (state.currentExecution?.id === id) {
                state.currentExecution = null;
              }
            }
          }),

        failExecution: (id, error) =>
          set((state) => {
            if (state.activeExecutions[id]) {
              const execution = state.activeExecutions[id];
              execution.error = error;
              execution.endTime = new Date().toISOString();
              
              // Move to history
              state.executionHistory.unshift(execution);
              delete state.activeExecutions[id];
              
              // Clear current if it's this execution
              if (state.currentExecution?.id === id) {
                state.currentExecution = null;
              }
            }
          }),

        setCurrentExecution: (execution) =>
          set((state) => {
            state.currentExecution = execution;
          }),

        clearExecutionHistory: () =>
          set((state) => {
            state.executionHistory = [];
          }),

        // UI Actions
        setSidebarOpen: (open) =>
          set((state) => {
            state.sidebarOpen = open;
          }),

        setCurrentView: (view) =>
          set((state) => {
            state.currentView = view;
          }),

        setLoading: (key, loading) =>
          set((state) => {
            if (loading) {
              state.loading[key] = true;
            } else {
              delete state.loading[key];
            }
          }),

        setError: (key, error) =>
          set((state) => {
            if (error) {
              state.errors[key] = error;
            } else {
              delete state.errors[key];
            }
          }),

        clearErrors: () =>
          set((state) => {
            state.errors = {};
          }),

        // Notification Actions
        addNotification: (notification) =>
          set((state) => {
            const newNotification: AppNotification = {
              ...notification,
              id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
              timestamp: new Date().toISOString(),
              read: false,
            };
            state.notifications.unshift(newNotification);
            
            // Limit to 50 notifications
            if (state.notifications.length > 50) {
              state.notifications = state.notifications.slice(0, 50);
            }
          }),

        markNotificationRead: (id) =>
          set((state) => {
            const notification = state.notifications.find(n => n.id === id);
            if (notification) {
              notification.read = true;
            }
          }),

        removeNotification: (id) =>
          set((state) => {
            state.notifications = state.notifications.filter(n => n.id !== id);
          }),

        clearNotifications: () =>
          set((state) => {
            state.notifications = [];
          }),

        // Preference Actions
        updatePreferences: (updates) =>
          set((state) => {
            Object.assign(state.preferences, updates);
          }),

        resetPreferences: () =>
          set((state) => {
            state.preferences = { ...defaultPreferences };
          }),

        // Cache Actions
        setCache: (key, data, ttl = 300000) => // 5 minutes default TTL
          set((state) => {
            state.cache[key] = {
              data,
              timestamp: Date.now(),
              ttl,
            };
          }),

        getCache: (key) => {
          const cached = get().cache[key];
          if (!cached) return null;
          
          const now = Date.now();
          if (now - cached.timestamp > cached.ttl) {
            // Cache expired
            set((state) => {
              delete state.cache[key];
            });
            return null;
          }
          
          return cached.data;
        },

        clearCache: (key) =>
          set((state) => {
            if (key) {
              delete state.cache[key];
            } else {
              state.cache = {};
            }
          }),

        // Utility Actions
        reset: () =>
          set(() => ({ ...initialState })),
      })),
      {
        name: 'procure-ai-store',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          // Only persist certain parts of the state
          selectedAgent: state.selectedAgent,
          executionHistory: state.executionHistory.slice(0, 20), // Keep last 20
          preferences: state.preferences,
          sidebarOpen: state.sidebarOpen,
        }),
      }
    )
  )
);

// ========== SELECTORS ==========

export const useAgents = () => useAppStore((state) => ({
  agents: state.availableAgents,
  selectedAgent: state.selectedAgent,
  loading: state.agentsLoading,
  error: state.agentsError,
}));

export const useExecutions = () => useAppStore((state) => ({
  active: state.activeExecutions,
  history: state.executionHistory,
  current: state.currentExecution,
}));

export const useNotifications = () => useAppStore((state) => ({
  notifications: state.notifications,
  unreadCount: state.notifications.filter(n => !n.read).length,
}));

export const usePreferences = () => useAppStore((state) => state.preferences);

export const useUI = () => useAppStore((state) => ({
  sidebarOpen: state.sidebarOpen,
  currentView: state.currentView,
  loading: state.loading,
  errors: state.errors,
}));

// ========== HOOKS ==========

export const useOptimisticUpdate = () => {
  const store = useAppStore();
  
  return <T>(
    optimisticUpdate: () => void,
    asyncAction: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ) => {
    // Apply optimistic update immediately
    optimisticUpdate();
    
    // Execute async action
    return asyncAction()
      .then((result) => {
        onSuccess?.(result);
        return result;
      })
      .catch((error) => {
        // Revert optimistic update on error
        // This would need to be implemented based on specific use case
        onError?.(error);
        throw error;
      });
  };
};

export default useAppStore;
