/**
 * Agent Service - Handles communication with agent backend
 * Provides agent discovery, execution, and monitoring capabilities
 */

export interface AgentCapabilities {
  agent_id: string;
  agent_type: string;
  capabilities: string[];
  description: string;
  version: string;
  tier?: 'tier_1' | 'tier_2' | 'tier_3';
  status?: 'active' | 'inactive' | 'maintenance';
  workflow_steps?: string[];
  factor_compliance?: string[];
  input_schema?: Record<string, any>;
  output_schema?: Record<string, any>;
}

export interface AgentExecutionRequest {
  input: string;
  parameters?: Record<string, any>;
  options?: {
    stream?: boolean;
    timeout?: number;
    priority?: 'low' | 'medium' | 'high';
  };
}

export interface AgentExecutionResponse {
  success: boolean;
  execution_id?: string;
  result?: any;
  error?: string;
  status?: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  steps_completed?: string[];
  next_actions?: string[];
}

export interface WorkflowStatus {
  execution_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  current_step: string;
  steps_completed: string[];
  total_steps: number;
  started_at: string;
  updated_at: string;
  estimated_completion?: string;
  error?: string;
}

class AgentService {
  private baseUrl: string;
  private backendUrl: string;
  private timeout: number;

  constructor() {
    this.baseUrl = '/api/agent'; // Proxied through Vite to API Gateway
    this.backendUrl = 'http://localhost:8000'; // Direct backend connection
    this.timeout = 30000; // 30 seconds default timeout
  }

  /**
   * Discover available agents and their capabilities
   */
  async discoverAgents(): Promise<AgentCapabilities[]> {
    try {
      const response = await fetch(`${this.backendUrl}/api/capabilities`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        throw new Error(`Failed to discover agents: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Transform the capabilities response into AgentCapabilities array
      const agents: AgentCapabilities[] = [];

      if (data.rfq_agent) {
        agents.push({
          agent_id: data.rfq_agent.agent_id,
          agent_type: data.rfq_agent.agent_type,
          capabilities: data.rfq_agent.capabilities,
          description: data.rfq_agent.description,
          version: data.rfq_agent.version,
          tier: 'tier_1',
          status: 'active',
          workflow_steps: data.rfq_agent.workflow_steps || [],
          factor_compliance: data.rfq_agent.factor_compliance || []
        });
      }

      if (data.orchestrator && data.orchestrator.managed_agents) {
        data.orchestrator.managed_agents.forEach((agent: any) => {
          agents.push({
            agent_id: agent.agent_id,
            agent_type: agent.agent_type,
            capabilities: agent.capabilities,
            description: agent.description,
            version: agent.version,
            tier: 'tier_2',
            status: 'active',
            workflow_steps: [],
            factor_compliance: []
          });
        });
      }

      return agents;
    } catch (error) {
      console.error('Agent discovery failed:', error);
      // Return fallback agent list based on known agents
      return this.getFallbackAgents();
    }
  }

  /**
   * Get specific agent capabilities
   */
  async getAgentCapabilities(agentId: string): Promise<AgentCapabilities | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${agentId}/capabilities`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to get agent capabilities: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Failed to get capabilities for agent ${agentId}:`, error);
      return null;
    }
  }

  /**
   * Execute an agent with given input
   */
  async executeAgent(
    agentId: string,
    request: AgentExecutionRequest
  ): Promise<AgentExecutionResponse> {
    try {
      // Use orchestrator endpoint for RFQ workflow agent
      if (agentId === 'rfq_workflow_agent') {
        const orchestratorRequest = {
          rfq_request: request.input,
          region: request.parameters?.region || 'US',
          category: request.parameters?.category || 'general',
          budget_range: request.parameters?.budget_range,
          urgency: request.parameters?.urgency || 'medium',
          department: request.parameters?.department
        };

        const response = await fetch(`${this.backendUrl}/api/orchestrator/process`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(orchestratorRequest),
          signal: AbortSignal.timeout(request.options?.timeout || this.timeout),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          throw new Error(errorData?.error || `Agent execution failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return {
          success: data.success || true,
          execution_id: data.workflow_id,
          result: data,
          status: 'completed'
        };
      }

      // For other agents, route through orchestrator as a robust fallback
      // This ensures non-existent per-agent endpoints don't cause failures in dev
      const orchestratorRequest = {
        rfq_request: request.input,
        region: request.parameters?.region || 'US',
        category: request.parameters?.category || 'general',
        budget_range: request.parameters?.budget_range,
        urgency: request.parameters?.urgency || 'medium',
        department: request.parameters?.department
      };

      const response = await fetch(`${this.backendUrl}/api/orchestrator/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orchestratorRequest),
        signal: AbortSignal.timeout(request.options?.timeout || this.timeout),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || `Agent execution failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: data.success || true,
        execution_id: data.workflow_id,
        result: data,
        status: 'completed'
      };
    } catch (error) {
      console.error(`Agent execution failed for ${agentId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get workflow execution status
   */
  async getWorkflowStatus(executionId: string): Promise<WorkflowStatus | null> {
    try {
      const response = await fetch(`${this.baseUrl}/workflow/${executionId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to get workflow status: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Failed to get workflow status for ${executionId}:`, error);
      return null;
    }
  }

  /**
   * Pause a running workflow
   */
  async pauseWorkflow(executionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/workflow/${executionId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      return response.ok;
    } catch (error) {
      console.error(`Failed to pause workflow ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Resume a paused workflow
   */
  async resumeWorkflow(executionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/workflow/${executionId}/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      return response.ok;
    } catch (error) {
      console.error(`Failed to resume workflow ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Cancel a running workflow
   */
  async cancelWorkflow(executionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/workflow/${executionId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      return response.ok;
    } catch (error) {
      console.error(`Failed to cancel workflow ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Fallback agent list when discovery fails
   */
  private getFallbackAgents(): AgentCapabilities[] {
    return [
      {
        agent_id: 'rfq_agent',
        agent_type: 'workflow_orchestrator',
        capabilities: ['rfq_processing', 'market_research', 'vendor_discovery', 'document_generation', 'human_in_the_loop'],
        description: 'LangGraph-based RFQ workflow agent with human-in-the-loop approval',
        version: '1.0.0',
        workflow_steps: ['initialize', 'parse_request', 'market_research', 'vendor_discovery', 'human_approval', 'generate_rfq', 'send_rfq', 'finalize'],
      },
      {
        agent_id: 'market_research_agent',
        agent_type: 'research_specialist',
        capabilities: ['market_analysis', 'price_research', 'trend_analysis', 'competitive_intelligence'],
        description: 'Specialized agent for comprehensive market research and intelligence gathering',
        version: '1.0.0',
      },
      {
        agent_id: 'universal-supplier-discovery-agent',
        agent_type: 'supplier_discovery',
        capabilities: ['vendor_identification', 'vendor_qualification', 'market_mapping', 'tier_classification'],
        description: 'Universal supplier discovery and qualification agent',
        version: '1.0.0',
      },
      {
        agent_id: 'document_generation_agent',
        agent_type: 'document_generator',
        capabilities: ['rfq_document_creation', 'template_customization', 'compliance_checking', 'format_optimization'],
        description: 'Intelligent document generation agent for RFQ and procurement documents',
        version: '1.0.0',
      },
      {
        agent_id: 'multi_agent_orchestrator',
        agent_type: 'orchestrator',
        capabilities: ['multi_agent_coordination', 'workflow_optimization', 'result_aggregation', 'quality_assurance'],
        description: 'Coordinates multiple specialized agents for complex procurement workflows',
        version: '1.0.0',
      },
    ];
  }

  /**
   * Create WebSocket connection for real-time updates
   */
  createWebSocketConnection(executionId: string): WebSocket | null {
    try {
      const wsUrl = `ws://localhost:3101/ws/workflow/${executionId}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`WebSocket connected for execution ${executionId}`);
      };

      ws.onerror = (error) => {
        console.error(`WebSocket error for execution ${executionId}:`, error);
      };

      ws.onclose = () => {
        console.log(`WebSocket closed for execution ${executionId}`);
      };

      return ws;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      return null;
    }
  }

  /**
   * Poll for workflow status updates (fallback when WebSocket is not available)
   */
  async pollWorkflowStatus(
    executionId: string,
    onUpdate: (status: WorkflowStatus) => void,
    intervalMs: number = 2000
  ): Promise<() => void> {
    let isPolling = true;

    const poll = async () => {
      if (!isPolling) return;

      try {
        const status = await this.getWorkflowStatus(executionId);
        if (status) {
          onUpdate(status);

          // Stop polling if workflow is completed or failed
          if (status.status === 'completed' || status.status === 'failed') {
            isPolling = false;
            return;
          }
        }
      } catch (error) {
        console.error('Polling error:', error);
      }

      if (isPolling) {
        setTimeout(poll, intervalMs);
      }
    };

    // Start polling
    poll();

    // Return cleanup function
    return () => {
      isPolling = false;
    };
  }
}

// Export singleton instance
export const agentService = new AgentService();
export default agentService;
