/**
 * Agent Selector Component
 * Dynamic agent discovery and selection with capabilities display
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { <PERSON><PERSON> } from './ui/button';
import { Skeleton } from './ui/skeleton';
import { Alert, AlertDescription } from './ui/alert';
import { CheckCircle, Circle, Info, Zap, Settings, Users } from 'lucide-react';
import { agentService, type AgentCapabilities } from '../services/agentService';

interface AgentSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string, capabilities: AgentCapabilities) => void;
  disabled?: boolean;
}

export const AgentSelector: React.FC<AgentSelectorProps> = ({
  selectedAgentId,
  onAgentSelect,
  disabled = false,
}) => {
  const [agents, setAgents] = useState<AgentCapabilities[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedAgent, setExpandedAgent] = useState<string | null>(null);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);
      const discoveredAgents = await agentService.discoverAgents();
      setAgents(discoveredAgents);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load agents');
    } finally {
      setLoading(false);
    }
  };

  const handleAgentSelect = (agent: AgentCapabilities) => {
    if (disabled) return;
    onAgentSelect(agent.agent_id, agent);
  };

  const toggleAgentDetails = (agentId: string) => {
    setExpandedAgent(expandedAgent === agentId ? null : agentId);
  };

  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'workflow_orchestrator':
        return <Settings className="h-5 w-5" />;
      case 'research_specialist':
        return <Info className="h-5 w-5" />;
      case 'supplier_discovery':
        return <Users className="h-5 w-5" />;
      case 'document_generator':
        return <Zap className="h-5 w-5" />;
      case 'orchestrator':
        return <Settings className="h-5 w-5" />;
      default:
        return <Circle className="h-5 w-5" />;
    }
  };

  const getAgentTypeColor = (agentType: string) => {
    switch (agentType) {
      case 'workflow_orchestrator':
        return 'bg-blue-100 text-blue-800';
      case 'research_specialist':
        return 'bg-green-100 text-green-800';
      case 'supplier_discovery':
        return 'bg-purple-100 text-purple-800';
      case 'document_generator':
        return 'bg-orange-100 text-orange-800';
      case 'orchestrator':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Select Agent</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5].map((i) => (
            <Card key={i} className="p-4">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-3 w-full mb-3" />
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            onClick={loadAgents}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Select Agent</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={loadAgents}
          disabled={loading}
        >
          Refresh
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {agents.map((agent) => {
          const isSelected = selectedAgentId === agent.agent_id;
          const isExpanded = expandedAgent === agent.agent_id;

          return (
            <Card
              key={agent.agent_id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : disabled
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => handleAgentSelect(agent)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getAgentIcon(agent.agent_type)}
                    {isSelected ? (
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                    ) : (
                      <Circle className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                  <Badge className={getAgentTypeColor(agent.agent_type)}>
                    {agent.agent_type.replace('_', ' ')}
                  </Badge>
                </div>
                <CardTitle className="text-base">
                  {agent.agent_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </CardTitle>
                <CardDescription className="text-sm">
                  {agent.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div>
                    <p className="text-xs font-medium text-gray-600 mb-1">
                      Capabilities ({agent.capabilities.length})
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {agent.capabilities.slice(0, 3).map((capability) => (
                        <Badge
                          key={capability}
                          variant="secondary"
                          className="text-xs"
                        >
                          {capability.replace(/_/g, ' ')}
                        </Badge>
                      ))}
                      {agent.capabilities.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{agent.capabilities.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {agent.workflow_steps && (
                    <div>
                      <p className="text-xs font-medium text-gray-600 mb-1">
                        Workflow Steps ({agent.workflow_steps.length})
                      </p>
                      <div className="text-xs text-gray-500">
                        {agent.workflow_steps.slice(0, 2).join(' → ')}
                        {agent.workflow_steps.length > 2 && ' → ...'}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>v{agent.version}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleAgentDetails(agent.agent_id);
                      }}
                    >
                      {isExpanded ? 'Less' : 'More'}
                    </Button>
                  </div>

                  {isExpanded && (
                    <div className="mt-3 pt-3 border-t space-y-2">
                      {agent.capabilities.length > 3 && (
                        <div>
                          <p className="text-xs font-medium text-gray-600 mb-1">
                            All Capabilities
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {agent.capabilities.map((capability) => (
                              <Badge
                                key={capability}
                                variant="secondary"
                                className="text-xs"
                              >
                                {capability.replace(/_/g, ' ')}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {agent.factor_compliance && (
                        <div>
                          <p className="text-xs font-medium text-gray-600 mb-1">
                            12-Factor Compliance
                          </p>
                          <div className="space-y-1">
                            {agent.factor_compliance.map((factor) => (
                              <div key={factor} className="text-xs text-gray-600">
                                • {factor}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {agents.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No agents available</p>
          <Button variant="outline" onClick={loadAgents} className="mt-2">
            Retry Loading
          </Button>
        </div>
      )}
    </div>
  );
};

export default AgentSelector;
