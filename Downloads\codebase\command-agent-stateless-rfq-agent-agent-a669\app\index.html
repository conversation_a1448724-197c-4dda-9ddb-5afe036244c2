<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/langbase.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Font Preloading for Performance (FOUC Prevention) -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Critical CSS for Font Loading Optimization -->
    <style>
      /* Ensure text remains visible during font loading */
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        font-display: swap;
        visibility: visible;
        opacity: 1;
      }

      /* Prevent invisible text during font swap period */
      * {
        font-display: swap;
      }
    </style>

    <title>Agent App by Langbase</title>
    <link rel="stylesheet" href="/src/styles.css" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
