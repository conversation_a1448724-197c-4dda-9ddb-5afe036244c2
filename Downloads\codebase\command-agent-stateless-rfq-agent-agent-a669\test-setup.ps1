# PowerShell script to test the development setup
# Run this after starting all services

Write-Host "🧪 Testing Procure.ai Development Setup" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$testResults = @()

# Test Redis connection
Write-Host "`n📋 Testing Redis connection..." -ForegroundColor Yellow
try {
    $redisTest = Test-NetConnection -ComputerName localhost -Port 6379 -WarningAction SilentlyContinue
    if ($redisTest.TcpTestSucceeded) {
        Write-Host "✅ Redis connection: PASS" -ForegroundColor Green
        $testResults += "Redis: PASS"
    } else {
        Write-Host "❌ Redis connection: FAIL" -ForegroundColor Red
        $testResults += "Redis: FAIL"
    }
} catch {
    Write-Host "❌ Redis connection: ERROR" -ForegroundColor Red
    $testResults += "Redis: ERROR"
}

# Test Agent Service
Write-Host "`n📋 Testing Agent Service (port 8000)..." -ForegroundColor Yellow
try {
    $agentTest = Test-NetConnection -ComputerName localhost -Port 8000 -WarningAction SilentlyContinue
    if ($agentTest.TcpTestSucceeded) {
        Write-Host "✅ Agent Service port: PASS" -ForegroundColor Green
        $testResults += "Agent Service Port: PASS"
        
        # Try to make HTTP request
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Agent Service HTTP: PASS" -ForegroundColor Green
                $testResults += "Agent Service HTTP: PASS"
            } else {
                Write-Host "⚠️  Agent Service HTTP: Status $($response.StatusCode)" -ForegroundColor Yellow
                $testResults += "Agent Service HTTP: Status $($response.StatusCode)"
            }
        } catch {
            Write-Host "❌ Agent Service HTTP: FAIL - $($_.Exception.Message)" -ForegroundColor Red
            $testResults += "Agent Service HTTP: FAIL"
        }
    } else {
        Write-Host "❌ Agent Service port: FAIL" -ForegroundColor Red
        $testResults += "Agent Service Port: FAIL"
    }
} catch {
    Write-Host "❌ Agent Service: ERROR" -ForegroundColor Red
    $testResults += "Agent Service: ERROR"
}

# Test API Gateway
Write-Host "`n📋 Testing API Gateway (port 3101)..." -ForegroundColor Yellow
try {
    $gatewayTest = Test-NetConnection -ComputerName localhost -Port 3101 -WarningAction SilentlyContinue
    if ($gatewayTest.TcpTestSucceeded) {
        Write-Host "✅ API Gateway port: PASS" -ForegroundColor Green
        $testResults += "API Gateway Port: PASS"
        
        # Try to make HTTP request
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3101" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ API Gateway HTTP: PASS" -ForegroundColor Green
                $testResults += "API Gateway HTTP: PASS"
            } else {
                Write-Host "⚠️  API Gateway HTTP: Status $($response.StatusCode)" -ForegroundColor Yellow
                $testResults += "API Gateway HTTP: Status $($response.StatusCode)"
            }
        } catch {
            Write-Host "❌ API Gateway HTTP: FAIL - $($_.Exception.Message)" -ForegroundColor Red
            $testResults += "API Gateway HTTP: FAIL"
        }
    } else {
        Write-Host "❌ API Gateway port: FAIL" -ForegroundColor Red
        $testResults += "API Gateway Port: FAIL"
    }
} catch {
    Write-Host "❌ API Gateway: ERROR" -ForegroundColor Red
    $testResults += "API Gateway: ERROR"
}

# Test Frontend
Write-Host "`n📋 Testing Frontend (port 3100)..." -ForegroundColor Yellow
try {
    $frontendTest = Test-NetConnection -ComputerName localhost -Port 3100 -WarningAction SilentlyContinue
    if ($frontendTest.TcpTestSucceeded) {
        Write-Host "✅ Frontend port: PASS" -ForegroundColor Green
        $testResults += "Frontend Port: PASS"
        
        # Try to make HTTP request
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3100" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Frontend HTTP: PASS" -ForegroundColor Green
                $testResults += "Frontend HTTP: PASS"
            } else {
                Write-Host "⚠️  Frontend HTTP: Status $($response.StatusCode)" -ForegroundColor Yellow
                $testResults += "Frontend HTTP: Status $($response.StatusCode)"
            }
        } catch {
            Write-Host "❌ Frontend HTTP: FAIL - $($_.Exception.Message)" -ForegroundColor Red
            $testResults += "Frontend HTTP: FAIL"
        }
    } else {
        Write-Host "❌ Frontend port: FAIL" -ForegroundColor Red
        $testResults += "Frontend Port: FAIL"
    }
} catch {
    Write-Host "❌ Frontend: ERROR" -ForegroundColor Red
    $testResults += "Frontend: ERROR"
}

# Check environment files
Write-Host "`n📋 Checking environment configuration..." -ForegroundColor Yellow

if (Test-Path "agent_services\.env") {
    Write-Host "✅ agent_services\.env: EXISTS" -ForegroundColor Green
    $testResults += "Agent .env: EXISTS"
    
    # Check for required keys
    $envContent = Get-Content "agent_services\.env" -Raw
    if ($envContent -match "PERPLEXITY_API_KEY=.+") {
        Write-Host "✅ PERPLEXITY_API_KEY: SET" -ForegroundColor Green
        $testResults += "PERPLEXITY_API_KEY: SET"
    } else {
        Write-Host "❌ PERPLEXITY_API_KEY: NOT SET" -ForegroundColor Red
        $testResults += "PERPLEXITY_API_KEY: NOT SET"
    }
} else {
    Write-Host "❌ agent_services\.env: MISSING" -ForegroundColor Red
    $testResults += "Agent .env: MISSING"
}

if (Test-Path "app\.env") {
    Write-Host "✅ app\.env: EXISTS" -ForegroundColor Green
    $testResults += "App .env: EXISTS"
} else {
    Write-Host "❌ app\.env: MISSING" -ForegroundColor Red
    $testResults += "App .env: MISSING"
}

# Summary
Write-Host "`n📊 Test Summary" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green

$passCount = ($testResults | Where-Object { $_ -like "*PASS*" }).Count
$failCount = ($testResults | Where-Object { $_ -like "*FAIL*" -or $_ -like "*ERROR*" -or $_ -like "*NOT SET*" -or $_ -like "*MISSING*" }).Count
$totalTests = $testResults.Count

Write-Host "Total Tests: $totalTests" -ForegroundColor Cyan
Write-Host "Passed: $passCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "`n🎉 All tests passed! Your setup is ready!" -ForegroundColor Green
    Write-Host "Open http://localhost:3100 in your browser to start using the application." -ForegroundColor Cyan
} else {
    Write-Host "`n⚠️  Some tests failed. Please check the issues above and:" -ForegroundColor Yellow
    Write-Host "1. Ensure all services are running" -ForegroundColor Cyan
    Write-Host "2. Check environment configuration" -ForegroundColor Cyan
    Write-Host "3. Verify API keys are set correctly" -ForegroundColor Cyan
}

Write-Host "`nDetailed Results:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    if ($result -like "*PASS*") {
        Write-Host "✅ $result" -ForegroundColor Green
    } else {
        Write-Host "❌ $result" -ForegroundColor Red
    }
}
