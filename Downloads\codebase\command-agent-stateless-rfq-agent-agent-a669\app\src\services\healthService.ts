/**
 * Health Service - Service reliability and monitoring
 * Provides health checks, performance monitoring, and circuit breaker patterns
 */

export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  responseTime: number;
  lastCheck: string;
  uptime: number;
  version?: string;
  details?: Record<string, any>;
}

export interface PerformanceMetrics {
  service: string;
  timestamp: string;
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    cpuUsage?: number;
    memoryUsage?: number;
    activeConnections?: number;
  };
}

export interface CircuitBreakerState {
  service: string;
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailure?: string;
  nextRetry?: string;
  threshold: number;
  timeout: number;
}

class HealthService {
  private healthCache: Map<string, ServiceHealth> = new Map();
  private metricsHistory: Map<string, PerformanceMetrics[]> = new Map();
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private healthCheckInterval: number = 30000; // 30 seconds
  private metricsRetention: number = 3600000; // 1 hour
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeCircuitBreakers();
    this.startHealthChecks();
  }

  /**
   * Initialize circuit breakers for known services
   */
  private initializeCircuitBreakers() {
    const services = ['agent-service', 'api-gateway', 'redis'];
    
    services.forEach(service => {
      this.circuitBreakers.set(service, {
        service,
        state: 'closed',
        failureCount: 0,
        threshold: 5, // Open after 5 failures
        timeout: 60000, // 1 minute timeout
      });
    });
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.performHealthChecks();
    }, this.healthCheckInterval);

    // Initial health check
    this.performHealthChecks();
  }

  /**
   * Stop health checks
   */
  public stopHealthChecks() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Perform health checks for all services
   */
  private async performHealthChecks() {
    // Use absolute endpoints to avoid proxy interference
    const services = [
      { name: 'agent-service', url: 'http://localhost:8000/health' },
      // API gateway is optional in dev; if running on 3101, root should respond
      { name: 'api-gateway', url: 'http://localhost:3101/' },
      // Frontend health: check the root of the current origin
      { name: 'frontend', url: `${window.location.origin}/` },
    ];

    const healthPromises = services.map(service =>
      this.checkServiceHealth(service.name, service.url)
    );

    await Promise.allSettled(healthPromises);
  }

  /**
   * Check health of a specific service
   */
  public async checkServiceHealth(serviceName: string, endpoint: string): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Check circuit breaker state
      const circuitBreaker = this.circuitBreakers.get(serviceName);
      if (circuitBreaker && circuitBreaker.state === 'open') {
        if (Date.now() < (new Date(circuitBreaker.nextRetry || 0).getTime())) {
          // Circuit breaker is open and timeout hasn't expired
          return this.createUnhealthyStatus(serviceName, 'Circuit breaker open', startTime);
        } else {
          // Try to transition to half-open
          circuitBreaker.state = 'half-open';
        }
      }

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      const responseTime = Date.now() - startTime;
      const data = await response.json().catch(() => ({}));

      const health: ServiceHealth = {
        service: serviceName,
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString(),
        uptime: data.uptime || 0,
        version: data.version,
        details: data.details || {},
      };

      // Update circuit breaker on success
      if (circuitBreaker) {
        if (response.ok) {
          circuitBreaker.state = 'closed';
          circuitBreaker.failureCount = 0;
          delete circuitBreaker.lastFailure;
          delete circuitBreaker.nextRetry;
        } else {
          this.handleCircuitBreakerFailure(circuitBreaker);
        }
      }

      this.healthCache.set(serviceName, health);
      this.recordMetrics(serviceName, responseTime, response.ok);
      
      return health;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const health = this.createUnhealthyStatus(serviceName, error instanceof Error ? error.message : 'Unknown error', startTime);
      
      // Update circuit breaker on failure
      const circuitBreaker = this.circuitBreakers.get(serviceName);
      if (circuitBreaker) {
        this.handleCircuitBreakerFailure(circuitBreaker);
      }

      this.healthCache.set(serviceName, health);
      this.recordMetrics(serviceName, responseTime, false);
      
      return health;
    }
  }

  /**
   * Handle circuit breaker failure
   */
  private handleCircuitBreakerFailure(circuitBreaker: CircuitBreakerState) {
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailure = new Date().toISOString();

    if (circuitBreaker.failureCount >= circuitBreaker.threshold) {
      circuitBreaker.state = 'open';
      circuitBreaker.nextRetry = new Date(Date.now() + circuitBreaker.timeout).toISOString();
    }
  }

  /**
   * Create unhealthy status
   */
  private createUnhealthyStatus(serviceName: string, error: string, startTime: number): ServiceHealth {
    return {
      service: serviceName,
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      uptime: 0,
      details: { error },
    };
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(serviceName: string, responseTime: number, success: boolean) {
    const now = new Date().toISOString();
    const metrics: PerformanceMetrics = {
      service: serviceName,
      timestamp: now,
      metrics: {
        responseTime,
        throughput: 1, // This would be calculated over time
        errorRate: success ? 0 : 1,
      },
    };

    if (!this.metricsHistory.has(serviceName)) {
      this.metricsHistory.set(serviceName, []);
    }

    const history = this.metricsHistory.get(serviceName)!;
    history.push(metrics);

    // Clean up old metrics
    const cutoff = Date.now() - this.metricsRetention;
    const filtered = history.filter(m => new Date(m.timestamp).getTime() > cutoff);
    this.metricsHistory.set(serviceName, filtered);
  }

  /**
   * Get current health status for all services
   */
  public getAllHealthStatus(): ServiceHealth[] {
    return Array.from(this.healthCache.values());
  }

  /**
   * Get health status for a specific service
   */
  public getServiceHealth(serviceName: string): ServiceHealth | null {
    return this.healthCache.get(serviceName) || null;
  }

  /**
   * Get performance metrics for a service
   */
  public getServiceMetrics(serviceName: string, timeRange?: number): PerformanceMetrics[] {
    const history = this.metricsHistory.get(serviceName) || [];
    
    if (!timeRange) {
      return history;
    }

    const cutoff = Date.now() - timeRange;
    return history.filter(m => new Date(m.timestamp).getTime() > cutoff);
  }

  /**
   * Get aggregated metrics for a service
   */
  public getAggregatedMetrics(serviceName: string, timeRange: number = 3600000) {
    const metrics = this.getServiceMetrics(serviceName, timeRange);
    
    if (metrics.length === 0) {
      return null;
    }

    const responseTimes = metrics.map(m => m.metrics.responseTime);
    const errors = metrics.filter(m => m.metrics.errorRate > 0).length;

    return {
      service: serviceName,
      timeRange,
      totalRequests: metrics.length,
      avgResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      errorRate: (errors / metrics.length) * 100,
      uptime: ((metrics.length - errors) / metrics.length) * 100,
    };
  }

  /**
   * Get circuit breaker states
   */
  public getCircuitBreakerStates(): CircuitBreakerState[] {
    return Array.from(this.circuitBreakers.values());
  }

  /**
   * Get circuit breaker state for a specific service
   */
  public getCircuitBreakerState(serviceName: string): CircuitBreakerState | null {
    return this.circuitBreakers.get(serviceName) || null;
  }

  /**
   * Manually reset a circuit breaker
   */
  public resetCircuitBreaker(serviceName: string): boolean {
    const circuitBreaker = this.circuitBreakers.get(serviceName);
    if (!circuitBreaker) {
      return false;
    }

    circuitBreaker.state = 'closed';
    circuitBreaker.failureCount = 0;
    delete circuitBreaker.lastFailure;
    delete circuitBreaker.nextRetry;

    return true;
  }

  /**
   * Check if a service is available (considering circuit breaker)
   */
  public isServiceAvailable(serviceName: string): boolean {
    const health = this.getServiceHealth(serviceName);
    const circuitBreaker = this.getCircuitBreakerState(serviceName);

    if (!health) {
      return false;
    }

    if (circuitBreaker && circuitBreaker.state === 'open') {
      return false;
    }

    return health.status === 'healthy' || health.status === 'degraded';
  }

  /**
   * Get overall system health
   */
  public getSystemHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: ServiceHealth[];
    summary: {
      total: number;
      healthy: number;
      degraded: number;
      unhealthy: number;
    };
  } {
    const services = this.getAllHealthStatus();
    
    const summary = {
      total: services.length,
      healthy: services.filter(s => s.status === 'healthy').length,
      degraded: services.filter(s => s.status === 'degraded').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
    };

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      services,
      summary,
    };
  }

  /**
   * Subscribe to health status changes
   */
  public onHealthChange(callback: (health: ServiceHealth[]) => void): () => void {
    const interval = setInterval(() => {
      callback(this.getAllHealthStatus());
    }, this.healthCheckInterval);

    return () => clearInterval(interval);
  }
}

// Export singleton instance
export const healthService = new HealthService();
export default healthService;
