import React from "react";

type Props = {
  results: any;
  onReset: () => void;
};

export function RFQResults({ results, onReset }: Props) {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold mb-4">RFQ Results</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
          {JSON.stringify(results, null, 2)}
        </pre>
        <button
          onClick={onReset}
          className="mt-4 inline-flex items-center px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
        >
          Start New RFQ
        </button>
      </div>
    </div>
  );
}

