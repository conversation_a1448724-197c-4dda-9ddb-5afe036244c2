import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { registerAgentProxyEndpoint } from './agent_proxy';

// Create Hono app
const app = new Hono();

// Middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', cors());
app.use('*', secureHeaders());

// Routes
app.get('/', c => {
	return c.json({
		message: 'Agent App Server',
		version: '1.0.0'
	});
});

// Register agent endpoint
registerAgentProxyEndpoint(app);

// Health check endpoints
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    details: {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
    }
  });
});

app.get('/api/health/frontend', (c) => {
  return c.json({
    service: 'frontend',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    details: {
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || '3101',
    }
  });
});

// Agent discovery endpoint
app.get('/api/agents/discover', async (c) => {
  try {
    // This would typically call the agent service to discover available agents
    const agents = [
      {
        agent_id: 'rfq_agent',
        agent_type: 'RFQ Orchestrator',
        description: 'Comprehensive RFQ workflow management with market research and vendor outreach',
        capabilities: ['market_research', 'vendor_discovery', 'document_generation', 'communication'],
        input_schema: {
          type: 'object',
          properties: {
            product_description: { type: 'string', description: 'Detailed product or service description' },
            quantity: { type: 'number', description: 'Required quantity' },
            budget_range: { type: 'string', description: 'Budget constraints' },
            timeline: { type: 'string', description: 'Required delivery timeline' },
            location: { type: 'string', description: 'Delivery location' },
            quality_requirements: { type: 'string', description: 'Quality specifications' },
          },
          required: ['product_description', 'quantity']
        },
        workflow_steps: [
          'Input Validation',
          'Market Research',
          'Vendor Discovery',
          'RFQ Document Generation',
          'Vendor Communication',
          'Response Analysis'
        ],
        twelve_factor_compliance: {
          codebase: true,
          dependencies: true,
          config: true,
          backing_services: true,
          build_release_run: true,
          processes: true,
          port_binding: true,
          concurrency: true,
          disposability: true,
          dev_prod_parity: true,
          logs: true,
          admin_processes: true
        }
      },
      {
        agent_id: 'market_research_agent',
        agent_type: 'Market Intelligence',
        description: 'Specialized market research and competitive analysis',
        capabilities: ['market_analysis', 'price_research', 'trend_analysis'],
        input_schema: {
          type: 'object',
          properties: {
            product_category: { type: 'string', description: 'Product category to research' },
            market_region: { type: 'string', description: 'Geographic market region' },
            research_depth: { type: 'string', enum: ['basic', 'detailed', 'comprehensive'] },
          },
          required: ['product_category']
        },
        workflow_steps: [
          'Market Segmentation',
          'Competitive Analysis',
          'Price Research',
          'Trend Analysis',
          'Report Generation'
        ],
        twelve_factor_compliance: {
          codebase: true,
          dependencies: true,
          config: true,
          backing_services: true,
          build_release_run: true,
          processes: true,
          port_binding: true,
          concurrency: true,
          disposability: true,
          dev_prod_parity: true,
          logs: true,
          admin_processes: true
        }
      },
      {
        agent_id: 'document_generation_agent',
        agent_type: 'Document Generator',
        description: 'Professional document generation and formatting',
        capabilities: ['rfq_documents', 'contracts', 'reports', 'templates'],
        input_schema: {
          type: 'object',
          properties: {
            document_type: { type: 'string', enum: ['rfq', 'contract', 'report'] },
            template: { type: 'string', description: 'Document template to use' },
            data: { type: 'object', description: 'Data to populate the document' },
          },
          required: ['document_type', 'data']
        },
        workflow_steps: [
          'Template Selection',
          'Data Validation',
          'Document Generation',
          'Formatting',
          'Quality Check'
        ],
        twelve_factor_compliance: {
          codebase: true,
          dependencies: true,
          config: true,
          backing_services: false,
          build_release_run: true,
          processes: true,
          port_binding: true,
          concurrency: true,
          disposability: true,
          dev_prod_parity: true,
          logs: true,
          admin_processes: true
        }
      }
    ];

    return c.json({ success: true, agents });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to discover agents'
    }, 500);
  }
});

// Export endpoint for results
app.post('/api/export/:executionId', async (c) => {
  const executionId = c.req.param('executionId');
  const { format, data, includeCharts } = await c.req.json();

  try {
    // This would implement actual export functionality
    // For now, return a mock response
    const exportData = {
      executionId,
      format,
      timestamp: new Date().toISOString(),
      data,
      includeCharts,
    };

    // Set appropriate headers based on format
    const headers: Record<string, string> = {};
    let filename = `rfq-results-${executionId}`;

    switch (format) {
      case 'pdf':
        headers['Content-Type'] = 'application/pdf';
        filename += '.pdf';
        break;
      case 'excel':
        headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename += '.xlsx';
        break;
      case 'json':
        headers['Content-Type'] = 'application/json';
        filename += '.json';
        break;
    }

    headers['Content-Disposition'] = `attachment; filename="${filename}"`;

    // For demo purposes, return JSON data
    // In production, this would generate actual files
    return c.json(exportData, 200, headers);
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Export failed'
    }, 500);
  }
});

// WebSocket endpoint for real-time updates (placeholder)
app.get('/api/ws/:executionId', (c) => {
  // This would implement WebSocket connection
  // For now, return information about WebSocket endpoint
  return c.json({
    message: 'WebSocket endpoint',
    executionId: c.req.param('executionId'),
    wsUrl: `ws://localhost:${process.env.PORT || '3101'}/ws/${c.req.param('executionId')}`
  });
});

// 404 handler
app.notFound(c => {
	return c.json(
		{
			status: 404,
			message: 'Not Found'
		},
		404
	);
});

// Error handler
app.onError((err, c) => {
	console.error(`${err}`);

	const isDev = process.env.NODE_ENV === 'development';

	return c.json(
		{
			status: 500,
			message: 'Internal Server Error',
			...(isDev && { error: err.message, stack: err.stack })
		},
		500
	);
});

export { app };
