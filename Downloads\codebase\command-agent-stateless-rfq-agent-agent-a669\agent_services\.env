# ========== APPLICATION SETTINGS ==========
APP_NAME=LangGraph AI Agent
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info

# ========== DATABASE CONFIGURATION ==========
# Supabase Configuration (Required)
SUPABASE_URL=https://demo.supabase.co
SUPABASE_ANON_KEY=demo_key
SUPABASE_SERVICE_ROLE_KEY=demo_service_key

# Legacy PostgreSQL Configuration (Optional)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# ========== REDIS CONFIGURATION ==========
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=100

# ========== API KEYS ==========
# Required: Get from https://www.perplexity.ai/
PERPLEXITY_API_KEY=demo_perplexity_key

# ========== SECURITY ==========
SECRET_KEY=development-key-change-in-production
JWT_SECRET=jwt-secret-change-in-production
ENCRYPTION_KEY=encryption-key-change-in-production

# ========== CORS SETTINGS ==========
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3100,http://localhost:3101

# ========== RATE LIMITING ==========
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ========== WORKFLOW CONFIGURATION ==========
MAX_CONCURRENT_WORKFLOWS=50
CONTEXT_WINDOW_SIZE=32000
WORKFLOW_TIMEOUT_SECONDS=300

# ========== MONITORING ==========
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# ========== MEMORY CONFIGURATION ==========
MEMORY_CONSOLIDATION_INTERVAL=3600
MAX_MEMORY_ENTRIES=10000
MEMORY_SIMILARITY_THRESHOLD=0.8

# ========== TOOL CONFIGURATION ==========
TOOL_TIMEOUT_SECONDS=30
MAX_TOOL_RETRIES=3
ENABLE_TOOL_VALIDATION=true

# ========== AGENT CONFIGURATION ==========
AGENT_RESPONSE_TIMEOUT=120
MAX_AGENT_ITERATIONS=10
ENABLE_HUMAN_APPROVAL=true
