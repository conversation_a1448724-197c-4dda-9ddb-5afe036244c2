# Corrected Application Deployment and Integration Guide

This guide provides step-by-step instructions for running the integrated multi-service application based on the **actual current codebase structure**.

## Prerequisites

Before starting, ensure you have the following installed:
- Node.js (version 18 or higher)
- npm (Node Package Manager)
- Redis server (either Docker or local installation)

## Current Application Architecture

The application consists of three main services:

1. **Agent Service** (Backend) - Located in `agent_services/`
2. **API Gateway** (Hono Server) - Located in `app/api/`
3. **Frontend Application** (React/Vite) - Located in `app/src/`

## Application Startup Sequence

Each service requires its own terminal session and must be started in the following order:

### Step 1: Initialize Redis Server

The Agent Service requires Redis for caching and rate limiting functionality.

**Option A - Using Docker (Recommended):**
```bash
docker run --name procure-ai-redis -p 6379:6379 -d redis:latest
```

**Option B - Using Local Redis Installation:**
- **Linux/macOS:**
  ```bash
  sudo service redis-server start
  ```
- **Windows:** Start Redis through your installation method or Windows Services

**Verification:** Confirm Redis is running by checking that port 6379 is accessible.

### Step 2: Start the Agent Service (Backend)

This service hosts all LangGraph agent implementations and provides the core AI functionality.

**Commands:**
```bash
cd agent_services
npm install
npx tsx main.ts
```

**Expected Result:** Service starts on `http://localhost:8000`
**Verification:** Check terminal output for successful startup messages and no error logs.

### Step 3: Start the API Gateway (Hono Server)

This middleware service routes requests between the frontend and Agent Service.

**Commands:**
```bash
cd app
npm install
npm run dev:server
```

**Expected Result:** Gateway starts on `http://localhost:3101`
**Verification:** Confirm the server starts without port conflicts or dependency errors.

### Step 4: Start the Frontend Application (Vite)

This React-based user interface provides the main application experience.

**Commands:**
```bash
cd app
npm run dev:vite
```

**Expected Result:** Frontend starts on `http://localhost:3100` (or next available port)
**Verification:** Browser should automatically open, or manually navigate to the provided URL.

## Configuration Requirements

### Environment Variables

**Agent Service (`agent_services/.env`):**
```bash
# Required API Keys
PERPLEXITY_API_KEY=your_perplexity_api_key_here
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Service Configuration
PORT=8000
NODE_ENV=development
LOG_LEVEL=info
```

**API Gateway (`app/.env`):**
```bash
# Development Configuration
NODE_ENV=development
PORT=3101

# Agent Service URL (currently hardcoded in app/api/agent_proxy.ts)
AGENT_SERVICE_URL=http://localhost:8000
```

### Service Communication Verification

After all services are running, verify the complete integration:

1. **Access the frontend URL** in your browser (typically `http://localhost:3100`)
2. **Test basic functionality** to ensure frontend → API Gateway → Agent Service communication works
3. **Check browser developer tools** and service logs for any connection errors
4. **Verify Redis connection** by checking agent service logs for Redis connectivity

## Troubleshooting Common Issues

### Port Conflicts
```bash
# Check if ports are in use
netstat -an | findstr :8000
netstat -an | findstr :3101
netstat -an | findstr :3100

# Kill processes using ports (Windows)
taskkill /F /PID <process_id>
```

### Redis Connection Issues
```bash
# Test Redis connectivity
redis-cli ping
# Should return "PONG"

# Check Redis logs if using Docker
docker logs procure-ai-redis
```

### Service Dependencies
- Ensure **Redis is running** before starting the Agent Service
- Ensure **Agent Service is running** before testing API Gateway functionality
- Check that all **npm install** commands completed successfully

### Environment Variables
- Verify all required environment variables are set
- Check that API keys are valid and have proper permissions
- Ensure Redis URL is accessible from the Agent Service

## Development Workflow

### Making Changes

1. **Agent Service Changes:**
   - Edit files in `agent_services/`
   - Restart with `npx tsx main.ts`

2. **API Gateway Changes:**
   - Edit files in `app/api/`
   - Restart with `npm run dev:server`

3. **Frontend Changes:**
   - Edit files in `app/src/`
   - Vite will auto-reload changes

### Testing Integration

1. **Health Check Endpoints:**
   - Agent Service: `http://localhost:8000/health` (if implemented)
   - API Gateway: `http://localhost:3101/health` (if implemented)

2. **Basic Functionality Test:**
   - Open frontend in browser
   - Submit a test RFQ request
   - Monitor all three service logs for proper request flow

## Next Steps for Development

### Phase 2: Enhanced User Experience (Timeline: 3-5 days)

**Primary Goal:** Enable user interaction with individual agents and orchestrated multi-agent workflows with real-time feedback.

**Key Areas:**
1. **Agent Selection Interface** - Dynamic agent discovery and selection UI
2. **Real-Time Progress Monitoring** - WebSocket integration for live status updates
3. **Rich Result Presentation** - Interactive data visualizations and export functionality

### Phase 3: Production-Ready Features (Timeline: 1-2 weeks)

**Primary Goal:** Implement enterprise-grade features for scalability, reliability, and maintainability.

**Key Areas:**
1. **Robust State Management** - Redux/Zustand implementation for complex application state
2. **Service Reliability & Monitoring** - Health checks, performance monitoring, circuit breakers
3. **Performance & Scalability Optimization** - Caching strategies, lazy loading, database optimization

## Success Criteria

- All three services start successfully without errors
- Frontend can communicate with API Gateway
- API Gateway can proxy requests to Agent Service
- Agent Service can connect to Redis
- Basic RFQ workflow functionality works end-to-end

## 🚀 Quick Start Options

### Option 1: Automated Setup (Recommended)
```bash
# Windows
.\start-dev.ps1

# Linux/macOS
./start-dev.sh
```

### Option 2: Manual Setup
Follow the step-by-step instructions above.

## 🧪 Testing Your Setup

After starting all services, run the test script:
```bash
# Windows
.\test-setup.ps1
```

This will verify:
- ✅ Redis connectivity
- ✅ All service ports are accessible
- ✅ HTTP endpoints are responding
- ✅ Environment files are configured

## 📁 Additional Files Created

This corrected deployment guide includes:

1. **QUICK_START.md** - Comprehensive quick start guide
2. **start-dev.ps1** - Windows automated setup script
3. **start-dev.sh** - Linux/macOS automated setup script
4. **test-setup.ps1** - Setup verification script
5. **agent_services/.env.example** - Agent service environment template
6. **app/.env.example** - Updated app environment template

## 🔄 Development Workflow Summary

1. **Start Redis**: `docker run --name procure-ai-redis -p 6379:6379 -d redis:latest`
2. **Configure Environment**: Copy `.env.example` files and add API keys
3. **Install Dependencies**: `npm install` in both `agent_services/` and `app/`
4. **Start Services**: Use automated scripts or manual commands
5. **Verify Setup**: Run test script and open http://localhost:3100
6. **Start Developing**: Make changes and test integration

**Ready for development and testing! 🚀**
