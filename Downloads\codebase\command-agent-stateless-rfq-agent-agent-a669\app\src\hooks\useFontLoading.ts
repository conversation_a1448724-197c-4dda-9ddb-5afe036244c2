/**
 * React Hook for Font Loading Management
 * Provides font loading status and utilities for React components
 */

import { useState, useEffect, useCallback } from 'react';
import { FontLoader, FontLoadingOptions } from '../utils/fontLoader';

export interface FontLoadingState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  loadedFonts: string[];
  loadingFonts: string[];
}

export const useFontLoading = () => {
  const [state, setState] = useState<FontLoadingState>({
    isLoading: true,
    isLoaded: false,
    hasError: false,
    loadedFonts: [],
    loadingFonts: [],
  });

  const fontLoader = FontLoader.getInstance();

  // Update state based on font loader status
  const updateState = useCallback(() => {
    const status = fontLoader.getFontLoadingStatus();
    const isLoading = status.loading.length > 0;
    const isLoaded = status.loaded.length > 0 && status.loading.length === 0;
    
    setState(prevState => ({
      ...prevState,
      isLoading,
      isLoaded,
      loadedFonts: status.loaded,
      loadingFonts: status.loading,
    }));
  }, [fontLoader]);

  // Load a specific font
  const loadFont = useCallback(async (options: FontLoadingOptions) => {
    try {
      setState(prevState => ({ ...prevState, isLoading: true, hasError: false }));
      await fontLoader.loadFont(options);
      updateState();
    } catch (error) {
      console.warn('Font loading failed:', error);
      setState(prevState => ({ ...prevState, hasError: true, isLoading: false }));
    }
  }, [fontLoader, updateState]);

  // Check if a specific font is loaded
  const isFontLoaded = useCallback((fontFamily: string, fallbackFonts?: string[]) => {
    return fontLoader.isFontLoaded(fontFamily, fallbackFonts);
  }, [fontLoader]);

  // Listen for font loading events
  useEffect(() => {
    const handleFontsLoaded = (event: CustomEvent) => {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        isLoaded: true,
        hasError: false,
        loadedFonts: event.detail.loaded,
        loadingFonts: event.detail.loading,
      }));
    };

    const handleFontLoadError = () => {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        hasError: true,
      }));
    };

    // Listen for custom font loading events
    window.addEventListener('fontsLoaded', handleFontsLoaded as EventListener);
    window.addEventListener('fontLoadError', handleFontLoadError);

    // Initial state update
    updateState();

    // Check if fonts are already loaded (for late-mounting components)
    if (document.documentElement.classList.contains('fonts-loaded')) {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        isLoaded: true,
        hasError: false,
      }));
    } else if (document.documentElement.classList.contains('fonts-fallback')) {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        isLoaded: false,
        hasError: true,
      }));
    }

    return () => {
      window.removeEventListener('fontsLoaded', handleFontsLoaded as EventListener);
      window.removeEventListener('fontLoadError', handleFontLoadError);
    };
  }, [updateState]);

  return {
    ...state,
    loadFont,
    isFontLoaded,
    updateState,
  };
};

/**
 * Hook for components that need to wait for fonts before rendering
 */
export const useWaitForFonts = (timeout: number = 3000) => {
  const [ready, setReady] = useState(false);
  const { isLoaded, hasError } = useFontLoading();

  useEffect(() => {
    if (isLoaded || hasError) {
      setReady(true);
      return;
    }

    const timer = setTimeout(() => {
      setReady(true); // Render with fallback fonts after timeout
    }, timeout);

    return () => clearTimeout(timer);
  }, [isLoaded, hasError, timeout]);

  return ready;
};

/**
 * Hook for getting font loading CSS classes
 */
export const useFontLoadingClasses = () => {
  const { isLoading, isLoaded, hasError } = useFontLoading();

  return {
    fontLoadingClass: isLoading ? 'font-loading' : '',
    fontLoadedClass: isLoaded ? 'fonts-loaded' : '',
    fontFallbackClass: hasError ? 'fonts-fallback' : '',
    combinedClass: [
      isLoading ? 'font-loading' : '',
      isLoaded ? 'fonts-loaded' : '',
      hasError ? 'fonts-fallback' : '',
    ].filter(Boolean).join(' '),
  };
};
