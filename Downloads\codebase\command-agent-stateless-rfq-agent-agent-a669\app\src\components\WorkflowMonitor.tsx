/**
 * Workflow Monitor Component
 * Real-time progress monitoring with WebSocket support and fallback polling
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  AlertCircle, 
  Pause, 
  Play, 
  Square,
  Wifi,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import { agentService, type WorkflowStatus } from '../services/agentService';

interface WorkflowMonitorProps {
  executionId: string;
  onComplete?: (status: WorkflowStatus) => void;
  onError?: (error: string) => void;
  showControls?: boolean;
}

interface WorkflowStep {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  duration?: number;
  error?: string;
}

export const WorkflowMonitor: React.FC<WorkflowMonitorProps> = ({
  executionId,
  onComplete,
  onError,
  showControls = true,
}) => {
  const [status, setStatus] = useState<WorkflowStatus | null>(null);
  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionType, setConnectionType] = useState<'websocket' | 'polling' | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isControlling, setIsControlling] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const pollCleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!executionId) return;

    // Try WebSocket first, fallback to polling
    initializeConnection();

    return () => {
      cleanup();
    };
  }, [executionId]);

  const initializeConnection = () => {
    // Try WebSocket connection first
    const ws = agentService.createWebSocketConnection(executionId);
    
    if (ws) {
      wsRef.current = ws;
      setConnectionType('websocket');
      
      ws.onopen = () => {
        setIsConnected(true);
        setError(null);
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleStatusUpdate(data);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onerror = () => {
        setIsConnected(false);
        setError('WebSocket connection failed, falling back to polling');
        fallbackToPolling();
      };
      
      ws.onclose = () => {
        setIsConnected(false);
        if (status?.status === 'running') {
          fallbackToPolling();
        }
      };
    } else {
      fallbackToPolling();
    }
  };

  const fallbackToPolling = () => {
    if (pollCleanupRef.current) {
      pollCleanupRef.current();
    }
    
    setConnectionType('polling');
    
    const cleanup = agentService.pollWorkflowStatus(
      executionId,
      handleStatusUpdate,
      2000 // Poll every 2 seconds
    );
    
    pollCleanupRef.current = cleanup;
    setIsConnected(true);
    setError(null);
  };

  const handleStatusUpdate = (newStatus: WorkflowStatus) => {
    setStatus(newStatus);
    updateSteps(newStatus);
    
    if (newStatus.status === 'completed') {
      onComplete?.(newStatus);
      setIsConnected(false);
    } else if (newStatus.status === 'failed') {
      onError?.(newStatus.error || 'Workflow failed');
      setIsConnected(false);
    }
  };

  const updateSteps = (status: WorkflowStatus) => {
    // Create steps based on workflow progress
    const allSteps = [
      'initialize',
      'parse_request', 
      'market_research',
      'vendor_discovery',
      'human_approval',
      'generate_rfq',
      'send_rfq',
      'finalize'
    ];

    const newSteps: WorkflowStep[] = allSteps.map((stepName, index) => {
      const isCompleted = status.steps_completed.includes(stepName);
      const isCurrent = status.current_step === stepName;
      const isPending = index > status.steps_completed.length;

      return {
        name: stepName,
        status: isCompleted ? 'completed' : isCurrent ? 'running' : isPending ? 'pending' : 'pending',
      };
    });

    setSteps(newSteps);
  };

  const cleanup = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (pollCleanupRef.current) {
      pollCleanupRef.current();
      pollCleanupRef.current = null;
    }
    
    setIsConnected(false);
  };

  const handlePause = async () => {
    setIsControlling(true);
    try {
      const success = await agentService.pauseWorkflow(executionId);
      if (!success) {
        setError('Failed to pause workflow');
      }
    } catch (err) {
      setError('Error pausing workflow');
    } finally {
      setIsControlling(false);
    }
  };

  const handleResume = async () => {
    setIsControlling(true);
    try {
      const success = await agentService.resumeWorkflow(executionId);
      if (!success) {
        setError('Failed to resume workflow');
      }
    } catch (err) {
      setError('Error resuming workflow');
    } finally {
      setIsControlling(false);
    }
  };

  const handleCancel = async () => {
    setIsControlling(true);
    try {
      const success = await agentService.cancelWorkflow(executionId);
      if (!success) {
        setError('Failed to cancel workflow');
      }
    } catch (err) {
      setError('Error canceling workflow');
    } finally {
      setIsControlling(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStepIcon = (stepStatus: string) => {
    switch (stepStatus) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Circle className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  if (!status) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow Monitor</CardTitle>
          <CardDescription>Loading workflow status...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Workflow Monitor
              <Badge className={getStatusColor(status.status)}>
                {status.status.toUpperCase()}
              </Badge>
            </CardTitle>
            <CardDescription>
              Execution ID: {executionId}
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-sm text-gray-500">
              {isConnected ? (
                <>
                  <Wifi className="h-4 w-4 text-green-500" />
                  {connectionType}
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-500" />
                  disconnected
                </>
              )}
            </div>
            
            {showControls && status.status === 'running' && (
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handlePause}
                  disabled={isControlling}
                >
                  <Pause className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isControlling}
                >
                  <Square className="h-4 w-4" />
                </Button>
              </div>
            )}
            
            {showControls && status.status === 'paused' && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleResume}
                disabled={isControlling}
              >
                <Play className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Progress Overview */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{status.progress}%</span>
          </div>
          <Progress value={status.progress} className="h-2" />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Started: {new Date(status.started_at).toLocaleTimeString()}</span>
            <span>
              Duration: {formatDuration(status.started_at)}
            </span>
          </div>
        </div>

        {/* Workflow Steps */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Workflow Steps</h4>
          <div className="space-y-2">
            {steps.map((step, index) => (
              <div
                key={step.name}
                className={`flex items-center gap-3 p-2 rounded-lg ${
                  step.status === 'running' ? 'bg-blue-50' : 
                  step.status === 'completed' ? 'bg-green-50' : 
                  step.status === 'failed' ? 'bg-red-50' : 'bg-gray-50'
                }`}
              >
                {getStepIcon(step.status)}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {step.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                    {step.status === 'running' && (
                      <Badge variant="secondary" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                  {step.error && (
                    <p className="text-xs text-red-600 mt-1">{step.error}</p>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  {index + 1}/{steps.length}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Estimated Completion */}
        {status.estimated_completion && status.status === 'running' && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span>
              Estimated completion: {new Date(status.estimated_completion).toLocaleTimeString()}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default WorkflowMonitor;
