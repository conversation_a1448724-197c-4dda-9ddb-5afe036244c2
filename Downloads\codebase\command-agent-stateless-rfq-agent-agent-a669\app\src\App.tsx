import React, { Suspense, useEffect, useMemo, useState } from 'react';
import { AgentSelector } from './components/AgentSelector';
import { DynamicAgentForm } from './components/DynamicAgentForm';
import { WorkflowMonitor } from './components/WorkflowMonitor';
import { ResultsVisualization } from './components/ResultsVisualization';
import { PerformanceDashboard } from './components/PerformanceDashboard';
import { LoadingSpinner } from './components/LoadingSpinner';
import { Button } from './components/ui/button';
import { Badge } from './components/ui/badge';
import { Toaster, toast } from 'sonner';
import { Activity, Bell, Bot, History, Menu, Settings, X } from 'lucide-react';
import { agentService, type AgentCapabilities, type AgentExecutionRequest } from './services/agentService';
import { healthService } from './services/healthService';

// App Views
type View = 'home' | 'agents' | 'execution' | 'results' | 'monitoring' | 'settings';

interface CurrentExecution {
  id: string;
  status: string | null;
  result: any | null;
  agentCapabilities: AgentCapabilities;
}

function App() {
  const [currentView, setCurrentView] = useState<View>('home');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<AgentCapabilities | null>(null);
  const [loading, setLoading] = useState<{ execution?: boolean }>({});
  const [currentExecution, setCurrentExecution] = useState<CurrentExecution | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);

  // Health notifications (graceful degradation + monitoring)
  useEffect(() => {
    const unsubscribe = healthService.onHealthChange((services) => {
      const unhealthy = services.filter((s) => s.status === 'unhealthy');
      if (unhealthy.length > 0) {
        setUnreadCount((c) => c + unhealthy.length);
        toast.warning(`${unhealthy.length} service(s) unhealthy`, {
          description: unhealthy.map((s) => s.service).join(', '),
        });
      }
    });
    return unsubscribe;
  }, []);

  // Navigation model
  const navigationItems = useMemo(() => ([
    { id: 'home', label: 'Home', icon: Bot },
    { id: 'agents', label: 'Agents', icon: Activity },
    { id: 'execution', label: 'Execute', icon: Activity, disabled: !selectedAgent },
    { id: 'results', label: 'Results', icon: History },
    { id: 'monitoring', label: 'Monitoring', icon: Activity },
    { id: 'settings', label: 'Settings', icon: Settings },
  ] as const), [selectedAgent]);

  // Handlers
  const handleAgentSelect = (agentId: string, capabilities: AgentCapabilities) => {
    setSelectedAgent(capabilities);
    setCurrentView('execution');
  };

  const handleAgentExecution = async (request: AgentExecutionRequest) => {
    if (!selectedAgent) return;
    setLoading((l) => ({ ...l, execution: true }));
    try {
      const res = await agentService.executeAgent(selectedAgent.agent_id, request);
      if (res.success) {
        const execId = res.execution_id || `local-${Date.now()}`;
        setCurrentExecution({
          id: execId,
          status: res.status ?? null,
          result: res.result ?? null,
          agentCapabilities: selectedAgent,
        });
        toast.success(`Agent ${selectedAgent.agent_id} started`);
        setCurrentView('results');
      } else {
        toast.error(res.error || 'Execution failed');
      }
    } catch (e) {
      toast.error(e instanceof Error ? e.message : 'Unknown error');
    } finally {
      setLoading((l) => ({ ...l, execution: false }));
    }
  };

  const renderHome = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to Procure.ai</h1>
        <p className="text-xl text-gray-600 mb-8">Intelligent procurement automation with AI agents</p>
        <div className="flex justify-center gap-4">
          <Button onClick={() => setCurrentView('agents')} size="lg">Get Started</Button>
          <Button variant="outline" onClick={() => setCurrentView('monitoring')} size="lg">View Monitoring</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div className="bg-white p-6 rounded-lg shadow">
          <Bot className="h-12 w-12 text-blue-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">AI Agents</h3>
          <p className="text-gray-600">Specialized agents for market research, vendor discovery, and document generation</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <Activity className="h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Real-time Monitoring</h3>
          <p className="text-gray-600">Track workflow progress with live updates and performance metrics</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <History className="h-12 w-12 text-purple-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Rich Analytics</h3>
          <p className="text-gray-600">Interactive visualizations and comprehensive reporting</p>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (currentView) {
      case 'home':
        return renderHome();
      case 'agents':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <AgentSelector
              selectedAgentId={selectedAgent?.agent_id}
              onAgentSelect={handleAgentSelect}
            />
          </Suspense>
        );
      case 'execution':
        return selectedAgent ? (
          <Suspense fallback={<LoadingSpinner />}>
            <DynamicAgentForm
              agent={selectedAgent}
              onSubmit={handleAgentExecution}
              loading={!!loading.execution}
            />
          </Suspense>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No agent selected</p>
            <Button onClick={() => setCurrentView('agents')}>Select an Agent</Button>
          </div>
        );
      case 'results':
        return currentExecution ? (
          <div className="space-y-6">
            <Suspense fallback={<LoadingSpinner />}>
              <WorkflowMonitor
                executionId={currentExecution.id}
                onComplete={(status) => {
                  setCurrentExecution((c) => (c ? { ...c, status: status.status } : c));
                }}
              />
            </Suspense>
            {currentExecution.result && (
              <Suspense fallback={<LoadingSpinner />}>
                <ResultsVisualization
                  results={currentExecution.result}
                  executionId={currentExecution.id}
                  agentType={currentExecution.agentCapabilities.agent_type}
                />
              </Suspense>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No execution results available</p>
            <Button onClick={() => setCurrentView('execution')}>Start New Execution</Button>
          </div>
        );
      case 'monitoring':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <PerformanceDashboard />
          </Suspense>
        );
      case 'settings':
        return (
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-6">Settings</h2>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">Settings panel coming soon...</p>
            </div>
          </div>
        );
      default:
        return <div>Page not found</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 bg-white shadow-lg flex flex-col`}>
        <div className="p-4 border-b">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Bot className="h-5 w-5 text-white" />
            </div>
            {sidebarOpen && (
              <div>
                <h1 className="font-bold text-lg">Procure.ai</h1>
                <p className="text-xs text-gray-500">AI Procurement</p>
              </div>
            )}
          </div>
        </div>

        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => !item.disabled && setCurrentView(item.id as View)}
                  disabled={!!item.disabled}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                    currentView === item.id
                      ? 'bg-blue-100 text-blue-700'
                      : item.disabled
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {sidebarOpen && (
                    <span className="font-medium">{item.label}</span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-4 border-t">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="w-full"
          >
            {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white shadow-sm border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {navigationItems.find((item) => item.id === currentView)?.label || 'Dashboard'}
              </h2>
              {selectedAgent && (
                <p className="text-sm text-gray-500">
                  Selected: {selectedAgent.agent_id.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                </p>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Notifications */}
              <div className="relative">
                <Button variant="ghost" size="sm">
                  <Bell className="h-5 w-5" />
                  {unreadCount > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              </div>

              {/* System Status */}
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">System Healthy</span>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6 overflow-auto">{renderContent()}</main>
      </div>

      {/* Toasts */}
      <Toaster richColors position="top-right" />
    </div>
  );
}

export default App;

