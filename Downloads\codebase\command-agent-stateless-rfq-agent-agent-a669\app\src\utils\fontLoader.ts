/**
 * Font Loading Utilities
 * Handles font loading optimization and FOUC prevention
 */

export interface FontLoadingOptions {
  fontFamily: string;
  fallbackFonts: string[];
  timeout?: number;
  display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
}

export class FontLoader {
  private static instance: FontLoader;
  private loadedFonts = new Set<string>();
  private loadingPromises = new Map<string, Promise<void>>();

  static getInstance(): FontLoader {
    if (!FontLoader.instance) {
      FontLoader.instance = new FontLoader();
    }
    return FontLoader.instance;
  }

  /**
   * Load a font with proper error handling and fallback
   */
  async loadFont(options: FontLoadingOptions): Promise<void> {
    const { fontFamily, fallbackFonts, timeout = 3000, display = 'swap' } = options;
    const fontKey = `${fontFamily}-${fallbackFonts.join('-')}`;

    // Return existing promise if font is already loading
    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey)!;
    }

    // Return immediately if font is already loaded
    if (this.loadedFonts.has(fontKey)) {
      return Promise.resolve();
    }

    const loadingPromise = this.performFontLoad(fontFamily, fallbackFonts, timeout, display);
    this.loadingPromises.set(fontKey, loadingPromise);

    try {
      await loadingPromise;
      this.loadedFonts.add(fontKey);
    } catch (error) {
      console.warn(`Failed to load font ${fontFamily}:`, error);
      // Continue with fallback fonts
    } finally {
      this.loadingPromises.delete(fontKey);
    }
  }

  private async performFontLoad(
    fontFamily: string,
    fallbackFonts: string[],
    timeout: number,
    display: string
  ): Promise<void> {
    // Check if Font Loading API is supported
    if ('fonts' in document) {
      try {
        // Use Font Loading API for better control
        const fontFace = new FontFace(
          fontFamily,
          `url(https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2)`,
          {
            display: display as any,
            weight: '100 900',
            style: 'normal',
          }
        );

        await Promise.race([
          fontFace.load(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Font loading timeout')), timeout)
          )
        ]);

        document.fonts.add(fontFace);
        return;
      } catch (error) {
        console.warn('Font Loading API failed, falling back to CSS method:', error);
      }
    }

    // Fallback to CSS-based font loading
    return this.loadFontWithCSS(fontFamily, fallbackFonts, timeout);
  }

  private async loadFontWithCSS(
    fontFamily: string,
    fallbackFonts: string[],
    timeout: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const testString = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const fallbackFont = fallbackFonts[0] || 'monospace';
      
      // Create test elements
      const testElement = document.createElement('div');
      testElement.style.cssText = `
        position: absolute;
        left: -9999px;
        top: -9999px;
        font-size: 72px;
        font-family: ${fallbackFont};
        visibility: hidden;
      `;
      testElement.textContent = testString;
      document.body.appendChild(testElement);

      const fallbackWidth = testElement.offsetWidth;

      // Update to target font
      testElement.style.fontFamily = `${fontFamily}, ${fallbackFont}`;

      let attempts = 0;
      const maxAttempts = timeout / 50; // Check every 50ms

      const checkFont = () => {
        attempts++;
        const currentWidth = testElement.offsetWidth;

        if (currentWidth !== fallbackWidth) {
          // Font has loaded
          document.body.removeChild(testElement);
          resolve();
        } else if (attempts >= maxAttempts) {
          // Timeout reached
          document.body.removeChild(testElement);
          reject(new Error('Font loading timeout'));
        } else {
          // Continue checking
          setTimeout(checkFont, 50);
        }
      };

      checkFont();
    });
  }

  /**
   * Preload critical fonts
   */
  async preloadCriticalFonts(): Promise<void> {
    const criticalFonts: FontLoadingOptions[] = [
      {
        fontFamily: 'Inter',
        fallbackFonts: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
        display: 'swap',
        timeout: 2000,
      },
    ];

    const loadPromises = criticalFonts.map(font => this.loadFont(font));
    
    try {
      await Promise.allSettled(loadPromises);
    } catch (error) {
      console.warn('Some critical fonts failed to load:', error);
    }
  }

  /**
   * Check if a font is loaded
   */
  isFontLoaded(fontFamily: string, fallbackFonts: string[] = []): boolean {
    const fontKey = `${fontFamily}-${fallbackFonts.join('-')}`;
    return this.loadedFonts.has(fontKey);
  }

  /**
   * Get font loading status
   */
  getFontLoadingStatus(): {
    loaded: string[];
    loading: string[];
  } {
    return {
      loaded: Array.from(this.loadedFonts),
      loading: Array.from(this.loadingPromises.keys()),
    };
  }
}

/**
 * Initialize font loading on app start
 */
export const initializeFontLoading = async (): Promise<void> => {
  const fontLoader = FontLoader.getInstance();
  
  // Add font loading class to prevent FOUC
  document.documentElement.classList.add('font-loading');
  
  try {
    await fontLoader.preloadCriticalFonts();
    
    // Remove loading class once fonts are loaded
    document.documentElement.classList.remove('font-loading');
    document.documentElement.classList.add('fonts-loaded');
    
    // Dispatch custom event for components that need to know
    window.dispatchEvent(new CustomEvent('fontsLoaded', {
      detail: fontLoader.getFontLoadingStatus()
    }));
    
  } catch (error) {
    console.warn('Font loading initialization failed:', error);
    // Remove loading class anyway to prevent indefinite loading state
    document.documentElement.classList.remove('font-loading');
    document.documentElement.classList.add('fonts-fallback');
  }
};

/**
 * Hook for React components to use font loading status
 */
export const useFontLoading = () => {
  const fontLoader = FontLoader.getInstance();
  
  return {
    loadFont: (options: FontLoadingOptions) => fontLoader.loadFont(options),
    isFontLoaded: (fontFamily: string, fallbackFonts?: string[]) => 
      fontLoader.isFontLoaded(fontFamily, fallbackFonts),
    getStatus: () => fontLoader.getFontLoadingStatus(),
  };
};

// Export singleton instance
export const fontLoader = FontLoader.getInstance();
