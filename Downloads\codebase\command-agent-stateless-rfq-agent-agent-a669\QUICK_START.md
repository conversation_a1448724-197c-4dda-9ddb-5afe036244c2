# 🚀 Quick Start Guide - Procure.ai Development

This guide will get you up and running with the Procure.ai application in under 10 minutes.

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- [ ] **Node.js 18+** installed ([Download here](https://nodejs.org/))
- [ ] **npm** (comes with Node.js)
- [ ] **Redis server** (Docker recommended)
- [ ] **API Keys** (see configuration section below)

## ⚡ One-Command Setup

### Windows (PowerShell)
```powershell
.\start-dev.ps1
```

### Linux/macOS (Bash)
```bash
chmod +x start-dev.sh
./start-dev.sh
```

The script will automatically:
1. ✅ Check if Redis is running
2. ✅ Create environment files from examples
3. ✅ Install all dependencies
4. ✅ Start all three services in separate terminals

## 🔧 Manual Setup (Alternative)

If you prefer to start services manually:

### Step 1: Start Redis
```bash
# Using Docker (recommended)
docker run --name procure-ai-redis -p 6379:6379 -d redis:latest

# Verify Redis is running
docker ps | grep redis
```

### Step 2: Configure Environment Variables

**Create `agent_services/.env`:**
```bash
# Copy from example
cp agent_services/.env.example agent_services/.env

# Edit and add your API keys
# Required: PERPLEXITY_API_KEY, SUPABASE_URL, SUPABASE_ANON_KEY
```

**Create `app/.env`:**
```bash
# Copy from example  
cp app/.env.example app/.env
# Default values should work for development
```

### Step 3: Install Dependencies

**Agent Service:**
```bash
cd agent_services
npm install
cd ..
```

**App (Frontend + API Gateway):**
```bash
cd app
npm install
cd ..
```

### Step 4: Start Services (3 separate terminals)

**Terminal 1 - Agent Service (Backend):**
```bash
cd agent_services
npx tsx main.ts
# Should start on http://localhost:8000
```

**Terminal 2 - API Gateway:**
```bash
cd app
npm run dev:server
# Should start on http://localhost:3101
```

**Terminal 3 - Frontend:**
```bash
cd app
npm run dev:vite
# Should start on http://localhost:3100
```

## 🔑 Required API Keys

### Perplexity API Key
1. Go to [Perplexity AI](https://www.perplexity.ai/)
2. Sign up/login and get your API key
3. Add to `agent_services/.env`: `PERPLEXITY_API_KEY=your_key_here`

### Supabase Configuration
1. Go to [Supabase](https://supabase.com/)
2. Create a new project
3. Get your project URL and anon key from Settings > API
4. Add to `agent_services/.env`:
   ```
   SUPABASE_URL=your_project_url
   SUPABASE_ANON_KEY=your_anon_key
   ```

## 🌐 Access Points

Once all services are running:

| Service | URL | Purpose |
|---------|-----|---------|
| **Frontend** | http://localhost:3100 | Main user interface |
| **API Gateway** | http://localhost:3101 | Request routing |
| **Agent Service** | http://localhost:8000 | AI agent backend |

## ✅ Verification Steps

1. **Check Redis**: `docker ps | grep redis`
2. **Check Agent Service**: Open http://localhost:8000 (should show service info)
3. **Check API Gateway**: Open http://localhost:3101 (should show API info)
4. **Check Frontend**: Open http://localhost:3100 (should show React app)
5. **Test Integration**: Submit a test RFQ through the frontend

## 🐛 Common Issues & Solutions

### Redis Connection Failed
```bash
# Check if Redis is running
docker ps | grep redis

# Start Redis if not running
docker run --name procure-ai-redis -p 6379:6379 -d redis:latest
```

### Port Already in Use
```bash
# Windows - Check what's using the port
netstat -an | findstr :8000

# Linux/macOS - Check what's using the port
lsof -i :8000

# Kill the process using the port
# Windows: taskkill /F /PID <process_id>
# Linux/macOS: kill -9 <process_id>
```

### Missing API Keys
- Edit `agent_services/.env` and add required API keys
- Restart the agent service after adding keys

### Dependencies Installation Failed
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 🔄 Development Workflow

### Making Changes

1. **Agent Service Changes**: Edit files in `agent_services/`, restart with `npx tsx main.ts`
2. **API Gateway Changes**: Edit files in `app/api/`, restart with `npm run dev:server`
3. **Frontend Changes**: Edit files in `app/src/`, Vite will auto-reload

### Testing

1. **Basic Health Check**: All three URLs should be accessible
2. **Integration Test**: Submit a test RFQ through the frontend
3. **Check Logs**: Monitor all three terminal windows for errors

## 🎯 Next Steps

Once everything is running:

1. **Explore the Frontend**: Navigate through the UI components
2. **Test RFQ Workflow**: Submit a sample procurement request
3. **Check Agent Responses**: Monitor the agent service logs
4. **Customize Configuration**: Adjust settings in `.env` files as needed

## 📞 Need Help?

- **Check Logs**: Look at the terminal outputs for error messages
- **Verify Configuration**: Ensure all API keys are correctly set
- **Test Components**: Verify each service individually before testing integration
- **Review Documentation**: Check the main README.md for detailed architecture information

---

**🎉 You're ready to start developing with Procure.ai!**

Open http://localhost:3100 in your browser and start exploring the application.
