{"name": "agent-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev:vite": "vite", "dev:server": "cross-env NODE_ENV=development tsx watch api/index.ts", "dev": "concurrently \"npm run dev:vite\" \"npm run dev:server\"", "build": "vite build"}, "dependencies": {"@assistant-ui/react": "^0.10.24", "@assistant-ui/react-markdown": "^0.10.5", "@hono/node-server": "^1.14.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.1.0", "embla-carousel-react": "^8.6.0", "hono": "^4.7.10", "immer": "^10.1.3", "input-otp": "^1.4.2", "langbase": "^1.1.67", "lucide-react": "^0.525.0", "mermaid": "^11.8.1", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "9.8.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.2.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "concurrently": "^9.2.1", "cross-env": "^7.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "vite": "^7.1.7"}}