/**
 * Results Visualization Component
 * Interactive data visualizations, document previews, and export functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { 
  Download, 
  FileText, 
  BarChart3, 
  PieChart, 
  Map, 
  Users, 
  DollarSign,
  TrendingUp,
  Eye,
  Share2,
  Printer
} from 'lucide-react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

interface ResultsVisualizationProps {
  results: any;
  executionId: string;
  agentType: string;
}

interface VendorData {
  name: string;
  score: number;
  price: number;
  location: string;
  tier: string;
  capabilities: string[];
}

interface MarketData {
  category: string;
  avgPrice: number;
  marketShare: number;
  trend: 'up' | 'down' | 'stable';
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const ResultsVisualization: React.FC<ResultsVisualizationProps> = ({
  results,
  executionId,
  agentType,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'json'>('pdf');

  // Process results data for visualization
  const processedData = processResultsData(results, agentType);

  const handleExport = async (format: 'pdf' | 'excel' | 'json') => {
    try {
      const response = await fetch(`/api/export/${executionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          data: results,
          includeCharts: true,
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `rfq-results-${executionId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleShare = async () => {
    try {
      const shareUrl = `${window.location.origin}/results/${executionId}`;
      await navigator.share({
        title: 'RFQ Results',
        text: 'Check out these RFQ results',
        url: shareUrl,
      });
    } catch (error) {
      // Fallback to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/results/${executionId}`);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Vendors Found</p>
                <p className="text-2xl font-bold">{processedData.vendorCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Avg Price</p>
                <p className="text-2xl font-bold">${processedData.avgPrice?.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Quality Score</p>
                <p className="text-2xl font-bold">{processedData.avgQuality}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Map className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Regions</p>
                <p className="text-2xl font-bold">{processedData.regionCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Executive Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <p>{processedData.summary}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderVendorAnalysis = () => (
    <div className="space-y-6">
      {/* Vendor Comparison Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Comparison</CardTitle>
          <CardDescription>Quality score vs Price analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={processedData.vendors}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Bar yAxisId="left" dataKey="score" fill="#8884d8" name="Quality Score" />
              <Bar yAxisId="right" dataKey="price" fill="#82ca9d" name="Price ($)" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Vendor Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Vendor Tiers</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <RechartsPieChart>
                <Pie
                  data={processedData.tierDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label
                >
                  {processedData.tierDistribution.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Geographic Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={processedData.locationDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="location" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Vendor Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Vendor</th>
                  <th className="text-left p-2">Score</th>
                  <th className="text-left p-2">Price</th>
                  <th className="text-left p-2">Location</th>
                  <th className="text-left p-2">Tier</th>
                  <th className="text-left p-2">Capabilities</th>
                </tr>
              </thead>
              <tbody>
                {processedData.vendors.map((vendor: VendorData, index: number) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{vendor.name}</td>
                    <td className="p-2">
                      <Badge variant={vendor.score > 80 ? 'default' : 'secondary'}>
                        {vendor.score}%
                      </Badge>
                    </td>
                    <td className="p-2">${vendor.price.toLocaleString()}</td>
                    <td className="p-2">{vendor.location}</td>
                    <td className="p-2">
                      <Badge variant="outline">{vendor.tier}</Badge>
                    </td>
                    <td className="p-2">
                      <div className="flex flex-wrap gap-1">
                        {vendor.capabilities.slice(0, 2).map((cap) => (
                          <Badge key={cap} variant="secondary" className="text-xs">
                            {cap}
                          </Badge>
                        ))}
                        {vendor.capabilities.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{vendor.capabilities.length - 2}
                          </Badge>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderMarketIntelligence = () => (
    <div className="space-y-6">
      {/* Market Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Market Trends</CardTitle>
          <CardDescription>Price trends over time</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={processedData.marketTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="price" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Market Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Category Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {processedData.marketAnalysis.map((item: MarketData, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{item.category}</p>
                    <p className="text-sm text-gray-600">${item.avgPrice.toLocaleString()} avg</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm">{item.marketShare}% share</p>
                    <div className="flex items-center gap-1">
                      <TrendingUp 
                        className={`h-4 w-4 ${
                          item.trend === 'up' ? 'text-green-500' : 
                          item.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                        }`} 
                      />
                      <span className="text-xs capitalize">{item.trend}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Key Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {processedData.insights.map((insight: string, index: number) => (
                <Alert key={index}>
                  <AlertDescription>{insight}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderDocuments = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generated Documents</CardTitle>
          <CardDescription>RFQ documents and related materials</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {processedData.documents.map((doc: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="font-medium">{doc.name}</p>
                    <p className="text-sm text-gray-600">{doc.type} • {doc.size}</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                  <Button size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with Export Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Results Analysis</h2>
          <p className="text-gray-600">Execution ID: {executionId}</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </Button>
          <Button variant="outline" onClick={() => window.print()}>
            <Printer className="h-4 w-4 mr-1" />
            Print
          </Button>
          <Button onClick={() => handleExport(exportFormat)}>
            <Download className="h-4 w-4 mr-1" />
            Export {exportFormat.toUpperCase()}
          </Button>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="market">Market Intel</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          {renderOverview()}
        </TabsContent>
        
        <TabsContent value="vendors" className="mt-6">
          {renderVendorAnalysis()}
        </TabsContent>
        
        <TabsContent value="market" className="mt-6">
          {renderMarketIntelligence()}
        </TabsContent>
        
        <TabsContent value="documents" className="mt-6">
          {renderDocuments()}
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Helper function to process results data
function processResultsData(results: any, agentType: string) {
  // This would be customized based on the actual results structure
  return {
    vendorCount: results?.vendors?.length || 0,
    avgPrice: results?.market_intelligence?.average_price || 0,
    avgQuality: results?.overall_quality_score || 0,
    regionCount: new Set(results?.vendors?.map((v: any) => v.location) || []).size,
    summary: results?.workflow_summary || 'No summary available',
    vendors: results?.vendors || [],
    tierDistribution: [
      { name: 'Tier 1', value: 3 },
      { name: 'Tier 2', value: 5 },
      { name: 'Tier 3', value: 2 },
    ],
    locationDistribution: [
      { location: 'Mumbai', count: 4 },
      { location: 'Bangalore', count: 3 },
      { location: 'Delhi', count: 3 },
    ],
    marketTrends: [
      { month: 'Jan', price: 45000 },
      { month: 'Feb', price: 47000 },
      { month: 'Mar', price: 46000 },
      { month: 'Apr', price: 48000 },
      { month: 'May', price: 50000 },
    ],
    marketAnalysis: [
      { category: 'Laptops', avgPrice: 48000, marketShare: 35, trend: 'up' as const },
      { category: 'Desktops', avgPrice: 35000, marketShare: 25, trend: 'stable' as const },
      { category: 'Accessories', avgPrice: 5000, marketShare: 40, trend: 'down' as const },
    ],
    insights: [
      'Market prices have increased by 8% in the last quarter',
      'Tier 1 vendors offer better quality but at 15% premium',
      'Mumbai region has the highest vendor concentration',
    ],
    documents: [
      { name: 'RFQ Document.pdf', type: 'PDF', size: '2.3 MB' },
      { name: 'Vendor Comparison.xlsx', type: 'Excel', size: '1.1 MB' },
      { name: 'Market Analysis.pdf', type: 'PDF', size: '3.2 MB' },
    ],
  };
}

export default ResultsVisualization;
