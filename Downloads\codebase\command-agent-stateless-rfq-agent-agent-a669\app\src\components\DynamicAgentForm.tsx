/**
 * Dynamic Agent Form Component
 * Adapts input form based on selected agent capabilities and requirements
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Checkbox } from './ui/checkbox';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2, Send, AlertCircle } from 'lucide-react';
import { type AgentCapabilities, type AgentExecutionRequest } from '../services/agentService';

interface DynamicAgentFormProps {
  agent: AgentCapabilities;
  onSubmit: (request: AgentExecutionRequest) => void;
  loading?: boolean;
  disabled?: boolean;
}

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'number';
  required?: boolean;
  placeholder?: string;
  options?: string[];
  description?: string;
  validation?: (value: any) => string | null;
}

export const DynamicAgentForm: React.FC<DynamicAgentFormProps> = ({
  agent,
  onSubmit,
  loading = false,
  disabled = false,
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formFields, setFormFields] = useState<FormField[]>([]);

  useEffect(() => {
    // Generate form fields based on agent capabilities
    const fields = generateFormFields(agent);
    setFormFields(fields);
    
    // Initialize form data with default values
    const initialData: Record<string, any> = {};
    fields.forEach(field => {
      if (field.type === 'checkbox') {
        initialData[field.name] = false;
      } else {
        initialData[field.name] = '';
      }
    });
    setFormData(initialData);
    setErrors({});
  }, [agent]);

  const generateFormFields = (agent: AgentCapabilities): FormField[] => {
    const baseFields: FormField[] = [
      {
        name: 'input',
        label: 'Request Description',
        type: 'textarea',
        required: true,
        placeholder: getPlaceholderForAgent(agent),
        description: 'Describe your procurement needs in natural language',
        validation: (value) => {
          if (!value || value.trim().length < 10) {
            return 'Please provide a detailed description (at least 10 characters)';
          }
          return null;
        },
      },
    ];

    // Add agent-specific fields based on capabilities
    const additionalFields: FormField[] = [];

    if (agent.capabilities.includes('market_research') || agent.capabilities.includes('market_analysis')) {
      additionalFields.push(
        {
          name: 'region',
          label: 'Region',
          type: 'select',
          required: true,
          options: ['India', 'North America', 'Europe', 'Asia Pacific', 'Global'],
          description: 'Target market region for research',
        },
        {
          name: 'budget_range',
          label: 'Budget Range',
          type: 'select',
          options: ['Under $10K', '$10K - $50K', '$50K - $100K', '$100K - $500K', 'Over $500K'],
          description: 'Approximate budget for the procurement',
        }
      );
    }

    if (agent.capabilities.includes('vendor_discovery') || agent.capabilities.includes('supplier_discovery')) {
      additionalFields.push({
        name: 'vendor_requirements',
        label: 'Vendor Requirements',
        type: 'textarea',
        placeholder: 'Specify any specific vendor requirements (certifications, location, size, etc.)',
        description: 'Additional criteria for vendor selection',
      });
    }

    if (agent.capabilities.includes('document_generation') || agent.capabilities.includes('rfq_document_creation')) {
      additionalFields.push(
        {
          name: 'document_format',
          label: 'Document Format',
          type: 'select',
          options: ['PDF', 'Word', 'Excel', 'Custom Template'],
          description: 'Preferred format for generated documents',
        },
        {
          name: 'include_terms',
          label: 'Include Standard Terms',
          type: 'checkbox',
          description: 'Include standard terms and conditions',
        }
      );
    }

    // Common fields for all agents
    const commonFields: FormField[] = [
      {
        name: 'urgency',
        label: 'Urgency Level',
        type: 'select',
        required: true,
        options: ['low', 'medium', 'high', 'critical'],
        description: 'How urgent is this request?',
      },
      {
        name: 'department',
        label: 'Department',
        type: 'select',
        options: ['IT', 'HR', 'Finance', 'Operations', 'Marketing', 'Procurement', 'Other'],
        description: 'Requesting department',
      },
    ];

    // Advanced options
    const advancedFields: FormField[] = [
      {
        name: 'stream_response',
        label: 'Stream Response',
        type: 'checkbox',
        description: 'Receive real-time updates during processing',
      },
      {
        name: 'timeout',
        label: 'Timeout (seconds)',
        type: 'number',
        placeholder: '300',
        description: 'Maximum execution time (default: 300 seconds)',
        validation: (value) => {
          if (value && (isNaN(value) || value < 30 || value > 3600)) {
            return 'Timeout must be between 30 and 3600 seconds';
          }
          return null;
        },
      },
    ];

    return [...baseFields, ...additionalFields, ...commonFields, ...advancedFields];
  };

  const getPlaceholderForAgent = (agent: AgentCapabilities): string => {
    switch (agent.agent_type) {
      case 'workflow_orchestrator':
        return 'I need 10 business laptops for our IT department with urgent delivery to Bangalore office...';
      case 'research_specialist':
        return 'Research current market prices for enterprise laptops in the Indian market...';
      case 'supplier_discovery':
        return 'Find qualified suppliers for office furniture in the Mumbai region...';
      case 'document_generator':
        return 'Generate an RFQ document for server equipment procurement...';
      case 'orchestrator':
        return 'Coordinate a complete procurement workflow for office setup including furniture, IT equipment, and supplies...';
      default:
        return 'Describe your procurement needs in detail...';
    }
  };

  const handleInputChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    formFields.forEach(field => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label} is required`;
      } else if (field.validation && formData[field.name]) {
        const error = field.validation(formData[field.name]);
        if (error) {
          newErrors[field.name] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Prepare the request
    const { input, stream_response, timeout, ...parameters } = formData;
    
    const request: AgentExecutionRequest = {
      input: input.trim(),
      parameters: Object.fromEntries(
        Object.entries(parameters).filter(([_, value]) => value !== '' && value !== false)
      ),
      options: {
        stream: stream_response || false,
        timeout: timeout ? parseInt(timeout) * 1000 : undefined,
        priority: formData.urgency || 'medium',
      },
    };

    onSubmit(request);
  };

  const renderField = (field: FormField) => {
    const hasError = !!errors[field.name];
    const value = formData[field.name] || '';

    switch (field.type) {
      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className={hasError ? 'text-red-600' : ''}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={field.name}
              placeholder={field.placeholder}
              value={value}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={hasError ? 'border-red-500' : ''}
              rows={field.name === 'input' ? 4 : 3}
              disabled={disabled || loading}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {hasError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors[field.name]}
              </p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className={hasError ? 'text-red-600' : ''}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select
              value={value}
              onValueChange={(newValue) => handleInputChange(field.name, newValue)}
              disabled={disabled || loading}
            >
              <SelectTrigger className={hasError ? 'border-red-500' : ''}>
                <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {hasError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors[field.name]}
              </p>
            )}
          </div>
        );

      case 'checkbox':
        return (
          <div key={field.name} className="flex items-center space-x-2">
            <Checkbox
              id={field.name}
              checked={value}
              onCheckedChange={(checked) => handleInputChange(field.name, checked)}
              disabled={disabled || loading}
            />
            <Label htmlFor={field.name} className="text-sm">
              {field.label}
            </Label>
            {field.description && (
              <p className="text-sm text-gray-500 ml-2">({field.description})</p>
            )}
          </div>
        );

      case 'number':
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className={hasError ? 'text-red-600' : ''}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="number"
              placeholder={field.placeholder}
              value={value}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={hasError ? 'border-red-500' : ''}
              disabled={disabled || loading}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {hasError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors[field.name]}
              </p>
            )}
          </div>
        );

      default: // text
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className={hasError ? 'text-red-600' : ''}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="text"
              placeholder={field.placeholder}
              value={value}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={hasError ? 'border-red-500' : ''}
              disabled={disabled || loading}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {hasError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors[field.name]}
              </p>
            )}
          </div>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Configure {agent.agent_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          <Badge variant="secondary">{agent.agent_type.replace('_', ' ')}</Badge>
        </CardTitle>
        <CardDescription>
          {agent.description}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic fields */}
          <div className="space-y-4">
            {formFields.slice(0, 3).map(renderField)}
          </div>

          {/* Agent-specific fields */}
          {formFields.length > 3 && (
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900 border-b pb-2">
                Agent-Specific Configuration
              </h4>
              {formFields.slice(3, -4).map(renderField)}
            </div>
          )}

          {/* Common fields */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 border-b pb-2">
              General Settings
            </h4>
            {formFields.slice(-4, -2).map(renderField)}
          </div>

          {/* Advanced options */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 border-b pb-2">
              Advanced Options
            </h4>
            {formFields.slice(-2).map(renderField)}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="submit"
              disabled={disabled || loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Execute Agent
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default DynamicAgentForm;
