/**
 * Performance Dashboard Component
 * Real-time monitoring of service health, metrics, and circuit breaker states
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Cpu, 
  Database,
  RefreshCw,
  Server,
  Wifi,
  WifiOff,
  Zap
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar
} from 'recharts';
import { healthService, type ServiceHealth, type PerformanceMetrics, type CircuitBreakerState } from '../services/healthService';

export const PerformanceDashboard: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [services, setServices] = useState<ServiceHealth[]>([]);
  const [circuitBreakers, setCircuitBreakers] = useState<CircuitBreakerState[]>([]);
  const [metricsData, setMetricsData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadDashboardData();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadDashboardData, 5000); // Refresh every 5 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    try {
      const health = healthService.getSystemHealth();
      const breakers = healthService.getCircuitBreakerStates();
      
      setSystemHealth(health);
      setServices(health.services);
      setCircuitBreakers(breakers);
      
      // Load metrics for each service
      const metrics: Record<string, any> = {};
      for (const service of health.services) {
        const aggregated = healthService.getAggregatedMetrics(service.service);
        const history = healthService.getServiceMetrics(service.service, 300000); // Last 5 minutes
        
        metrics[service.service] = {
          aggregated,
          history: history.map(h => ({
            timestamp: new Date(h.timestamp).toLocaleTimeString(),
            responseTime: h.metrics.responseTime,
            errorRate: h.metrics.errorRate * 100,
          })),
        };
      }
      
      setMetricsData(metrics);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'unhealthy':
        return <WifiOff className="h-5 w-5 text-red-500" />;
      default:
        return <Wifi className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800';
      case 'unhealthy':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCircuitBreakerColor = (state: string) => {
    switch (state) {
      case 'closed':
        return 'bg-green-100 text-green-800';
      case 'half-open':
        return 'bg-yellow-100 text-yellow-800';
      case 'open':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleResetCircuitBreaker = (serviceName: string) => {
    healthService.resetCircuitBreaker(serviceName);
    loadDashboardData();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <p className="text-gray-600">Real-time system monitoring and health status</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className={`h-4 w-4 mr-1 ${autoRefresh ? 'animate-pulse' : ''}`} />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </Button>
          <Button variant="outline" size="sm" onClick={loadDashboardData}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              {getStatusIcon(systemHealth?.status)}
              <div>
                <p className="text-sm text-gray-600">System Status</p>
                <Badge className={getStatusColor(systemHealth?.status)}>
                  {systemHealth?.status?.toUpperCase()}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Services</p>
                <p className="text-2xl font-bold">
                  {systemHealth?.summary.healthy}/{systemHealth?.summary.total}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Circuit Breakers</p>
                <p className="text-2xl font-bold">
                  {circuitBreakers.filter(cb => cb.state === 'closed').length}/{circuitBreakers.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Avg Response</p>
                <p className="text-2xl font-bold">
                  {Math.round(
                    services.reduce((acc, s) => acc + s.responseTime, 0) / services.length || 0
                  )}ms
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="circuit-breakers">Circuit Breakers</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <div className="grid gap-4">
            {services.map((service) => (
              <Card key={service.service}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(service.status)}
                      <CardTitle className="text-lg">
                        {service.service.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </CardTitle>
                    </div>
                    <Badge className={getStatusColor(service.status)}>
                      {service.status.toUpperCase()}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Response Time</p>
                      <p className="font-semibold">{service.responseTime}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Last Check</p>
                      <p className="font-semibold">
                        {new Date(service.lastCheck).toLocaleTimeString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Uptime</p>
                      <p className="font-semibold">{Math.round(service.uptime / 1000)}s</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Version</p>
                      <p className="font-semibold">{service.version || 'N/A'}</p>
                    </div>
                  </div>
                  
                  {service.details?.error && (
                    <Alert className="mt-3">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{service.details.error}</AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          {Object.entries(metricsData).map(([serviceName, data]) => (
            <Card key={serviceName}>
              <CardHeader>
                <CardTitle>
                  {serviceName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Metrics
                </CardTitle>
                <CardDescription>Performance metrics over the last 5 minutes</CardDescription>
              </CardHeader>
              
              <CardContent>
                {data.aggregated && (
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6 text-sm">
                    <div>
                      <p className="text-gray-600">Avg Response</p>
                      <p className="font-semibold">{Math.round(data.aggregated.avgResponseTime)}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Min Response</p>
                      <p className="font-semibold">{data.aggregated.minResponseTime}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Max Response</p>
                      <p className="font-semibold">{data.aggregated.maxResponseTime}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Error Rate</p>
                      <p className="font-semibold">{data.aggregated.errorRate.toFixed(1)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Uptime</p>
                      <p className="font-semibold">{data.aggregated.uptime.toFixed(1)}%</p>
                    </div>
                  </div>
                )}
                
                {data.history.length > 0 && (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={data.history}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="timestamp" />
                        <YAxis />
                        <Tooltip />
                        <Line 
                          type="monotone" 
                          dataKey="responseTime" 
                          stroke="#8884d8" 
                          name="Response Time (ms)"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="circuit-breakers" className="space-y-4">
          <div className="grid gap-4">
            {circuitBreakers.map((cb) => (
              <Card key={cb.service}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      {cb.service.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge className={getCircuitBreakerColor(cb.state)}>
                        {cb.state.toUpperCase()}
                      </Badge>
                      {cb.state === 'open' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleResetCircuitBreaker(cb.service)}
                        >
                          Reset
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Failure Count</p>
                      <p className="font-semibold">{cb.failureCount}/{cb.threshold}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Last Failure</p>
                      <p className="font-semibold">
                        {cb.lastFailure ? new Date(cb.lastFailure).toLocaleTimeString() : 'None'}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Next Retry</p>
                      <p className="font-semibold">
                        {cb.nextRetry ? new Date(cb.nextRetry).toLocaleTimeString() : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Timeout</p>
                      <p className="font-semibold">{cb.timeout / 1000}s</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceDashboard;
